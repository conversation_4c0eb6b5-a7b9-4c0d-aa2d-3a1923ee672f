import os
import sys
import logging
from dotenv import load_dotenv
from datetime import datetime, timedel<PERSON>

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from app.utils.database import DatabaseManager, Appointment
from app.utils.odoo_integration import OdooIntegration
import psycopg2
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def print_environment_info():
    """Print current environment configuration."""
    print("\n=== Environment Configuration ===")
    print(f"ODOO_URL: {os.getenv('ODOO_URL', 'Not set')}")
    print(f"ODOO_DB: {os.getenv('ODOO_DB', 'Not set')}")
    print(f"ODOO_USERNAME: {os.getenv('ODOO_USERNAME', 'Not set')}")
    print(f"DB_NAME: {os.getenv('DB_NAME', 'Not set')}")
    print(f"DB_USER: {os.getenv('DB_USER', 'Not set')}")
    print(f"DB_HOST: {os.getenv('DB_HOST', 'Not set')}")
    print(f"DB_PORT: {os.getenv('DB_PORT', 'Not set')}")

def test_database_connection():
    """Test PostgreSQL database connection."""
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "beauty_center"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT")
        )
        print("✅ Successfully connected to PostgreSQL database")
        
        # Check if appointments table exists
        with conn.cursor() as cur:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'appointments'
                );
            """)
            table_exists = cur.fetchone()[0]
            if table_exists:
                print("✅ Appointments table exists")
            else:
                print("❌ Appointments table does not exist")
        
        conn.close()
    except Exception as e:
        print(f"❌ Failed to connect to PostgreSQL: {str(e)}")
        print("Please check your PostgreSQL configuration in the .env file")
        sys.exit(1)

def test_odoo_connection():
    """Test Odoo connection."""
    try:
        odoo = OdooIntegration()
        if odoo.uid:  # Check if we have a valid user ID from authentication
            print("✅ Successfully connected to Odoo")
            print(f"Odoo Version: {odoo.get_odoo_version()}")
        else:
            print("❌ Failed to connect to Odoo")
            print("Please check your Odoo configuration in the .env file")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error testing Odoo connection: {str(e)}")
        print("Please verify that:")
        print("1. Odoo server is running")
        print("2. XML-RPC is enabled in Odoo")
        print("3. The URL is correct and accessible")
        sys.exit(1)

def create_test_appointment():
    """Create a test appointment in PostgreSQL."""
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "beauty_center"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT")
        )
        
        # Create a test appointment
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO appointments 
                (client_name, phone_number, service_type, appointment_date, appointment_time, processed)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id;
            """, (
                "Test Client",
                "1234567890",
                "Haircut",
                datetime.now().date() + timedelta(days=1),
                "10:00:00",
                False
            ))
            appointment_id = cur.fetchone()[0]
            conn.commit()
            print(f"✅ Created test appointment with ID: {appointment_id}")
        
        conn.close()
        return appointment_id
    except Exception as e:
        print(f"❌ Failed to create test appointment: {str(e)}")
        return None

def test_odoo_integration():
    """Test Odoo integration with detailed verification steps."""
    try:
        # Load environment variables
        load_dotenv()
        
        # Log environment configuration
        logger.info("Testing Odoo Integration with configuration:")
        logger.info(f"ODOO_URL: {os.getenv('ODOO_URL')}")
        logger.info(f"ODOO_DB: {os.getenv('ODOO_DB')}")
        logger.info(f"ODOO_USERNAME: {os.getenv('ODOO_USERNAME')}")
        
        # Initialize database connection
        db = DatabaseManager()
        logger.info("Successfully connected to PostgreSQL database")
        
        # Verify Appointments table exists
        with db.conn.cursor() as cur:
            cur.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'appointments')")
            exists = cur.fetchone()[0]
            if not exists:
                logger.error("Appointments table does not exist!")
                return
            logger.info("Appointments table exists")
        
        # Initialize Odoo integration
        odoo = OdooIntegration()
        logger.info("Successfully connected to Odoo")
        logger.info(f"Odoo server version: {odoo.get_odoo_version()}")
        
        # Create a test appointment
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        appointment_data = {
            'client_name': 'Test Client',
            'phone_number': '1234567890',
            'service_type': 'Haircut',
            'appointment_date': tomorrow,
            'appointment_time': '14:00',
            'notes': 'Test appointment for Odoo integration'
        }
        
        logger.info("\n=== Testing Odoo Integration Tools ===")
        logger.info("1. Testing appointment creation in local database...")
        # Save to database
        appointment_id = db.save_appointment(appointment_data)
        logger.info(f"Created test appointment with ID: {appointment_id}")
        
        logger.info("\n2. Testing Odoo sync process (same as agent's sync)...")
        # Process appointments using the same method the agent uses
        result = odoo.process_appointments()
        logger.info(f"Processed appointments result: {result}")
        
        # Verify Odoo records
        logger.info("\n3. Verifying Odoo records created by the sync...")
        
        # Search for partner using the correct method
        logger.info("Searching for partner in Odoo...")
        partner_id = odoo.get_partner_id('1234567890')
        if partner_id:
            logger.info(f"Found partner with ID: {partner_id}")
            partner_data = odoo.get_partner_data(partner_id)
            logger.info(f"Partner details: {partner_data}")
        else:
            logger.error("Partner not found in Odoo!")
        
        # Search for lead using the correct method
        logger.info("\nSearching for lead in Odoo...")
        lead_id = odoo.get_lead_id(f"Appointment: {appointment_data['service_type']}")
        if lead_id:
            logger.info(f"Found lead with ID: {lead_id}")
            lead_data = odoo.get_lead_data(lead_id)
            logger.info(f"Lead details: {lead_data}")
        else:
            logger.error("Lead not found in Odoo!")
        
        # Search for calendar event using the correct method
        logger.info("\nSearching for calendar event in Odoo...")
        event_id = odoo.get_calendar_event_id(appointment_data['client_name'], tomorrow)
        if event_id:
            logger.info(f"Found calendar event with ID: {event_id}")
            event_data = odoo.get_calendar_event_data(event_id)
            logger.info(f"Event details: {event_data}")
        else:
            logger.error("Calendar event not found in Odoo!")
        
        # Clean up test data
        logger.info("\n4. Cleaning up test data...")
        with db.conn.cursor() as cur:
            cur.execute("DELETE FROM appointments WHERE id = %s", (appointment_id,))
            db.conn.commit()
        logger.info("Test data cleaned up")
        
        logger.info("\n=== Odoo Integration Test Complete ===")
        logger.info("This test verifies the same Odoo integration tools used by the agent.")
        logger.info("If this test succeeds, the agent's Odoo integration should work correctly.")
        
    except Exception as e:
        logger.error(f"Error during Odoo integration test: {str(e)}")
        raise

def test_recurring_appointment():
    """Test recurring appointment creation and sync."""
    try:
        # Initialize database and Odoo
        db = DatabaseManager()
        odoo = OdooIntegration()
        logger.info("Testing recurring appointment...")
        
        # Create test worker
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO workers (name)
                VALUES (%s)
                RETURNING id
            """, ("Test Worker",))
            worker_id = cur.fetchone()[0]
        
        # Create test recurring appointment
        recurrence_pattern = {
            'frequency': 'weekly',
            'interval': 1
        }
        
        test_appointment = {
            'client_name': 'Test Recurring Client',
            'phone_number': '1234567891',
            'service_type': 'Massage',
            'appointment_date': datetime.now().date() + timedelta(days=1),
            'appointment_time': '15:00',
            'location': 'Downtown Branch',
            'duration': 120,  # 2 hours
            'is_recurring': True,
            'recurrence_pattern': json.dumps(recurrence_pattern),
            'recurrence_end_date': datetime.now().date() + timedelta(days=30),
            'worker_id': worker_id
        }
        
        # Save appointment
        appointment_id = db.save_appointment(test_appointment)
        logger.info(f"Created test recurring appointment with ID: {appointment_id}")
        
        # Process appointment
        result = odoo.process_appointments()
        logger.info(f"Processed appointments result: {result}")
        
        # Verify Odoo records
        logger.info("\nVerifying Odoo records for recurring appointment...")
        
        # Search for partner
        partner_id = odoo.get_partner_id('1234567891')
        if partner_id:
            logger.info(f"Found partner with ID: {partner_id}")
            partner_data = odoo.get_partner_data(partner_id)
            logger.info(f"Partner details: {partner_data}")
        else:
            logger.error("Partner not found in Odoo!")
        
        # Search for lead
        lead_id = odoo.get_lead_id(f"Appointment: {test_appointment['service_type']}")
        if lead_id:
            logger.info(f"Found lead with ID: {lead_id}")
            lead_data = odoo.get_lead_data(lead_id)
            logger.info(f"Lead details: {lead_data}")
            
            # Verify lead contains appointment information
            description = lead_data.get('description', '')
            # Remove HTML tags for comparison
            clean_description = description.replace('<p>', '').replace('</p>', '')
            
            # Check for basic appointment information
            assert test_appointment['service_type'] in clean_description, "Lead missing service type"
            assert test_appointment['location'] in clean_description, "Lead missing location"
            assert str(test_appointment['duration']) in clean_description, "Lead missing duration"
            
            # For free version, we can only verify basic information
            logger.info("✅ Lead verification passed (free version limitations)")
        else:
            logger.error("Lead not found in Odoo!")
        
        # Search for calendar event
        event_id = odoo.get_calendar_event_id(test_appointment['client_name'], test_appointment['appointment_date'].strftime('%Y-%m-%d'))
        if event_id:
            logger.info(f"Found calendar event with ID: {event_id}")
            event_data = odoo.get_calendar_event_data(event_id)
            logger.info(f"Event details: {event_data}")
            
            # Verify event is recurring (if supported in free version)
            if 'recurrency' in event_data:
                assert event_data.get('recurrency', False), "Event not marked as recurring"
                assert event_data.get('rrule_type') == 'weekly', "Incorrect recurrence type"
                assert event_data.get('interval') == 1, "Incorrect recurrence interval"
                logger.info("✅ Calendar event recurrence verified")
            else:
                logger.info("⚠️ Recurrence verification skipped (free version limitation)")
        else:
            logger.error("Calendar event not found in Odoo!")
        
        # Clean up
        logger.info("\nCleaning up test data...")
        with db.get_cursor() as cur:
            cur.execute("DELETE FROM appointments WHERE id = %s", (appointment_id,))
            cur.execute("DELETE FROM workers WHERE id = %s", (worker_id,))
        logger.info("Test data cleaned up")
        
        logger.info("✅ Recurring appointment test passed!")
        
    except Exception as e:
        logger.error(f"Error during recurring appointment test: {str(e)}")
        raise

def test_invalid_appointment_validation():
    """Test validation of invalid appointments."""
    try:
        # Initialize database and Odoo
        db = DatabaseManager()
        odoo = OdooIntegration()
        logger.info("Testing invalid appointment validation...")
        
        # Test case 1: Missing required fields
        invalid_appointment = {
            'client_name': 'Test Invalid Client',
            'phone_number': '1234567892'
            # Missing required fields
        }
        
        # Test case 2: Invalid recurring appointment
        invalid_recurring = {
            'client_name': 'Test Invalid Recurring',
            'phone_number': '1234567893',
            'service_type': 'Massage',
            'appointment_date': datetime.now().date() + timedelta(days=1),
            'appointment_time': '16:00',
            'is_recurring': True
            # Missing recurrence pattern
        }
        
        # Test case 3: Invalid duration
        invalid_duration = {
            'client_name': 'Test Invalid Duration',
            'phone_number': '1234567894',
            'service_type': 'Massage',
            'appointment_date': datetime.now().date() + timedelta(days=1),
            'appointment_time': '17:00',
            'duration': -1  # Invalid duration
        }
        
        # Process appointments
        result = odoo.process_appointments()
        logger.info(f"Processed appointments result: {result}")
        
        # Verify errors were caught
        assert len(result.get('errors', [])) > 0, "No validation errors were caught"
        logger.info("✅ Invalid appointment validation test passed!")
        
    except Exception as e:
        logger.error(f"Error during invalid appointment test: {str(e)}")
        raise

if __name__ == "__main__":
    test_odoo_integration()
    test_recurring_appointment()
    test_invalid_appointment_validation() 