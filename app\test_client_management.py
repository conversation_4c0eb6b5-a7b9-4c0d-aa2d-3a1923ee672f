import os
from dotenv import load_dotenv
from utils.client_management import ClientManagement
import psycopg2
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

def test_client_management():
    """Test the complete client management functionality."""
    print("\n=== Testing Client Management ===")
    
    # Initialize client management
    client_mgmt = ClientManagement()
    
    # Test data
    test_client = {
        'client_name': 'Test Client',
        'phone_number': '**********',
        'location': 'Downtown',
        'contract_type': 'one-time',
        'service_type': 'Haircut',
        'most_asked_provider': '<PERSON>'
    }
    
    # Test 1: Create new client
    print("\n1. Testing client creation...")
    success, message = client_mgmt.process_client_data(test_client)
    print(f"Result: {'✅' if success else '❌'} {message}")
    
    # Test 2: Get client by phone
    print("\n2. Testing client retrieval...")
    client = client_mgmt.get_client_by_phone(test_client['phone_number'])
    if client:
        print("✅ Client found:")
        print(f"Name: {client['client_name']}")
        print(f"Phone: {client['phone_number']}")
        print(f"Location: {client['location']}")
        print(f"Contract Type: {client['contract_type']}")
        print(f"Service Type: {client['service_type']}")
        print(f"Provider: {client['most_asked_provider']}")
    else:
        print("❌ Client not found")
    
    # Test 3: Update client
    print("\n3. Testing client update...")
    update_data = {
        'phone_number': test_client['phone_number'],
        'location': 'Uptown',
        'service_type': 'Facial'
    }
    success, message = client_mgmt.process_client_data(update_data)
    print(f"Result: {'✅' if success else '❌'} {message}")
    
    # Verify update
    client = client_mgmt.get_client_by_phone(test_client['phone_number'])
    if client and client['location'] == 'Uptown':
        print("✅ Update verified")
        print(f"New location: {client['location']}")
        print(f"Updated services: {client['service_type']}")
    else:
        print("❌ Update verification failed")
    
    # Test 4: Contract type inference
    print("\n4. Testing contract type inference...")
    contract_type = client_mgmt.infer_contract_type(test_client['phone_number'])
    print(f"Inferred contract type: {contract_type}")
    
    # Test 5: Service type update
    print("\n5. Testing service type update...")
    success = client_mgmt.update_service_type(client['id'], 'Manicure')
    print(f"Result: {'✅' if success else '❌'} Service type updated")
    
    # Verify final client state
    print("\nFinal client state:")
    client = client_mgmt.get_client_by_phone(test_client['phone_number'])
    if client:
        print(f"Name: {client['client_name']}")
        print(f"Phone: {client['phone_number']}")
        print(f"Location: {client['location']}")
        print(f"Contract Type: {client['contract_type']}")
        print(f"Service Type: {client['service_type']}")
        print(f"Provider: {client['most_asked_provider']}")
    else:
        print("❌ Client not found")

if __name__ == "__main__":
    test_client_management() 