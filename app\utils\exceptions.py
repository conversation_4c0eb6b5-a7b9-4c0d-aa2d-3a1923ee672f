"""
Custom exception hierarchy and error handling utilities for the Smart Booking Agent.

This module provides a standardized approach to error handling across the application,
ensuring consistent error responses, proper logging, and security-conscious error messages.
"""

import logging
import traceback
from typing import Optional, Dict, Any, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

# Configure logger for this module
logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels for categorizing exceptions."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for better error classification."""
    VALIDATION = "validation"
    DATABASE = "database"
    EXTERNAL_API = "external_api"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"
    NETWORK = "network"
    CONFIGURATION = "configuration"


@dataclass
class ErrorContext:
    """Context information for errors."""
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    operation: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class BaseBookingException(Exception):
    """
    Base exception class for all booking agent exceptions.

    Provides standardized error handling with severity, category, and context.
    """

    def __init__(
        self,
        message: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None,
        original_exception: Optional[Exception] = None
    ):
        self.message = message
        self.severity = severity
        self.category = category
        self.user_message = user_message or self._get_default_user_message()
        self.context = context or ErrorContext()
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()

        super().__init__(self.message)

    def _get_default_user_message(self) -> str:
        """Get default user-friendly message based on category."""
        default_messages = {
            ErrorCategory.VALIDATION: "Please check your input and try again.",
            ErrorCategory.DATABASE: "We're experiencing technical difficulties. Please try again later.",
            ErrorCategory.EXTERNAL_API: "External service is temporarily unavailable. Please try again later.",
            ErrorCategory.AUTHENTICATION: "Authentication failed. Please check your credentials.",
            ErrorCategory.AUTHORIZATION: "You don't have permission to perform this action.",
            ErrorCategory.BUSINESS_LOGIC: "Unable to process your request. Please contact support.",
            ErrorCategory.SYSTEM: "An unexpected error occurred. Please try again later.",
            ErrorCategory.NETWORK: "Network connection issue. Please check your connection and try again.",
            ErrorCategory.CONFIGURATION: "Service configuration error. Please contact support."
        }
        return default_messages.get(self.category, "An error occurred. Please try again.")

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging and serialization."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "user_message": self.user_message,
            "severity": self.severity.value,
            "category": self.category.value,
            "timestamp": self.timestamp.isoformat(),
            "context": {
                "user_id": self.context.user_id,
                "request_id": self.context.request_id,
                "operation": self.context.operation,
                "additional_data": self.context.additional_data
            },
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class ValidationException(BaseBookingException):
    """Exception for validation errors."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[str] = None,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ):
        self.field = field
        self.value = value

        super().__init__(
            message=message,
            severity=ErrorSeverity.LOW,
            category=ErrorCategory.VALIDATION,
            user_message=user_message,
            context=context
        )

    def to_dict(self) -> Dict[str, Any]:
        """Extend base to_dict with validation-specific fields."""
        data = super().to_dict()
        data.update({
            "field": self.field,
            "value": self.value[:50] + "..." if self.value and len(self.value) > 50 else self.value
        })
        return data


class DatabaseException(BaseBookingException):
    """Exception for database-related errors."""

    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table: Optional[str] = None,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None,
        original_exception: Optional[Exception] = None
    ):
        self.operation = operation
        self.table = table

        super().__init__(
            message=message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.DATABASE,
            user_message=user_message,
            context=context,
            original_exception=original_exception
        )

    def to_dict(self) -> Dict[str, Any]:
        """Extend base to_dict with database-specific fields."""
        data = super().to_dict()
        data.update({
            "database_operation": self.operation,
            "table": self.table
        })
        return data


class ExternalAPIException(BaseBookingException):
    """Exception for external API errors."""

    def __init__(
        self,
        message: str,
        service: Optional[str] = None,
        status_code: Optional[int] = None,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None,
        original_exception: Optional[Exception] = None
    ):
        self.service = service
        self.status_code = status_code

        super().__init__(
            message=message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.EXTERNAL_API,
            user_message=user_message,
            context=context,
            original_exception=original_exception
        )

    def to_dict(self) -> Dict[str, Any]:
        """Extend base to_dict with API-specific fields."""
        data = super().to_dict()
        data.update({
            "service": self.service,
            "status_code": self.status_code
        })
        return data


class BusinessLogicException(BaseBookingException):
    """Exception for business logic violations."""

    def __init__(
        self,
        message: str,
        rule: Optional[str] = None,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ):
        self.rule = rule

        super().__init__(
            message=message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.BUSINESS_LOGIC,
            user_message=user_message,
            context=context
        )

    def to_dict(self) -> Dict[str, Any]:
        """Extend base to_dict with business logic fields."""
        data = super().to_dict()
        data.update({
            "business_rule": self.rule
        })
        return data


class ConfigurationException(BaseBookingException):
    """Exception for configuration errors."""

    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        user_message: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ):
        self.config_key = config_key

        super().__init__(
            message=message,
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.CONFIGURATION,
            user_message=user_message,
            context=context
        )

    def to_dict(self) -> Dict[str, Any]:
        """Extend base to_dict with configuration fields."""
        data = super().to_dict()
        data.update({
            "config_key": self.config_key
        })
        return data


# Legacy compatibility - maintain existing ValidationError for backward compatibility
ValidationError = ValidationException


def log_exception(
    exception: Union[BaseBookingException, Exception],
    context: Optional[ErrorContext] = None,
    include_traceback: bool = True
) -> None:
    """
    Log exception with appropriate level and structured format.

    Args:
        exception: Exception to log
        context: Additional context information
        include_traceback: Whether to include traceback in logs
    """
    if isinstance(exception, BaseBookingException):
        # Use structured logging for our custom exceptions
        log_data = exception.to_dict()

        # Add traceback if requested
        if include_traceback and exception.original_exception:
            log_data["traceback"] = traceback.format_exception(
                type(exception.original_exception),
                exception.original_exception,
                exception.original_exception.__traceback__
            )

        # Log at appropriate level based on severity
        if exception.severity == ErrorSeverity.CRITICAL:
            logger.critical("Critical error occurred", extra={"error_data": log_data})
        elif exception.severity == ErrorSeverity.HIGH:
            logger.error("High severity error occurred", extra={"error_data": log_data})
        elif exception.severity == ErrorSeverity.MEDIUM:
            logger.warning("Medium severity error occurred", extra={"error_data": log_data})
        else:
            logger.info("Low severity error occurred", extra={"error_data": log_data})

    else:
        # Handle generic exceptions
        error_context = context or ErrorContext()
        log_data = {
            "error_type": exception.__class__.__name__,
            "message": str(exception),
            "timestamp": datetime.utcnow().isoformat(),
            "context": {
                "user_id": error_context.user_id,
                "request_id": error_context.request_id,
                "operation": error_context.operation,
                "additional_data": error_context.additional_data
            }
        }

        if include_traceback:
            log_data["traceback"] = traceback.format_exception(
                type(exception), exception, exception.__traceback__
            )

        logger.error("Unhandled exception occurred", extra={"error_data": log_data})


def create_error_response(
    exception: Union[BaseBookingException, Exception],
    language: str = 'en',
    include_details: bool = False
) -> Dict[str, Any]:
    """
    Create standardized error response for API endpoints.

    Args:
        exception: Exception to create response for
        language: Response language
        include_details: Whether to include detailed error information (for debugging)

    Returns:
        Standardized error response dictionary
    """
    if isinstance(exception, BaseBookingException):
        response = {
            "error": True,
            "message": exception.user_message,
            "category": exception.category.value,
            "timestamp": exception.timestamp.isoformat()
        }

        if include_details:
            response.update({
                "details": {
                    "error_type": exception.__class__.__name__,
                    "severity": exception.severity.value,
                    "internal_message": exception.message,
                    "context": exception.context.__dict__ if exception.context else None
                }
            })

    else:
        # Generic exception handling
        response = {
            "error": True,
            "message": "An unexpected error occurred. Please try again later.",
            "category": ErrorCategory.SYSTEM.value,
            "timestamp": datetime.utcnow().isoformat()
        }

        if include_details:
            response.update({
                "details": {
                    "error_type": exception.__class__.__name__,
                    "severity": ErrorSeverity.HIGH.value,
                    "internal_message": str(exception)
                }
            })

    return response


def handle_database_error(
    operation: str,
    table: Optional[str] = None,
    original_exception: Optional[Exception] = None,
    context: Optional[ErrorContext] = None
) -> DatabaseException:
    """
    Create and log database exception with proper context.

    Args:
        operation: Database operation that failed
        table: Table involved in the operation
        original_exception: Original database exception
        context: Additional context

    Returns:
        DatabaseException instance
    """
    message = f"Database operation '{operation}' failed"
    if table:
        message += f" on table '{table}'"

    if original_exception:
        message += f": {str(original_exception)}"

    exception = DatabaseException(
        message=message,
        operation=operation,
        table=table,
        context=context,
        original_exception=original_exception
    )

    log_exception(exception)
    return exception


def handle_external_api_error(
    service: str,
    operation: str,
    status_code: Optional[int] = None,
    original_exception: Optional[Exception] = None,
    context: Optional[ErrorContext] = None
) -> ExternalAPIException:
    """
    Create and log external API exception with proper context.

    Args:
        service: External service name
        operation: Operation that failed
        status_code: HTTP status code if applicable
        original_exception: Original API exception
        context: Additional context

    Returns:
        ExternalAPIException instance
    """
    message = f"External API call to {service} failed during {operation}"
    if status_code:
        message += f" (HTTP {status_code})"

    if original_exception:
        message += f": {str(original_exception)}"

    exception = ExternalAPIException(
        message=message,
        service=service,
        status_code=status_code,
        context=context,
        original_exception=original_exception
    )

    log_exception(exception)
    return exception


def sanitize_error_for_user(error: Union[str, Exception]) -> str:
    """
    Sanitize error messages to prevent information disclosure.
    Enhanced version of the existing sanitize_error_message function.

    Args:
        error: Error message or exception

    Returns:
        Safe error message for user display
    """
    error_str = str(error).lower()

    # Check for sensitive information patterns
    sensitive_patterns = [
        'password', 'token', 'key', 'secret', 'database', 'connection',
        'sql', 'query', 'table', 'column', 'schema', 'host', 'port',
        'user', 'admin', 'root', 'config', 'env', 'path', 'file',
        'traceback', 'exception', 'stack', 'internal', 'system'
    ]

    for pattern in sensitive_patterns:
        if pattern in error_str:
            logger.warning(f"Sensitive information detected in error message: {pattern}")
            return "An error occurred while processing your request. Please try again."

    # Return sanitized version of the original error
    from app.utils.validation import sanitize_text_input
    return sanitize_text_input(str(error))


def create_standardized_error_response(
    error: Union[str, Exception],
    language: str = "en",
    user_id: str = None,
    operation: str = None,
    additional_context: Dict = None
) -> str:
    """
    Create standardized error response with logging and translation.
    Consolidates duplicated error response patterns from tools.py and other modules.

    Args:
        error: Error message or exception
        language: Response language for translation
        user_id: User identifier for context
        operation: Operation that failed
        additional_context: Additional context data

    Returns:
        Translated, sanitized error message ready for user display
    """
    # Import here to avoid circular imports
    from app.utils.language import translate_response

    # Create error context for logging
    context = ErrorContext(
        user_id=user_id,
        operation=operation,
        additional_data=additional_context or {}
    )

    # Log the exception if it's an Exception object
    if isinstance(error, Exception):
        log_exception(error, context=context)

    # Sanitize error message for user
    safe_message = sanitize_error_for_user(error)

    # Translate and return
    return translate_response(safe_message, language)


def create_standardized_success_response(
    message: str,
    language: str = "en",
    **format_args
) -> str:
    """
    Create standardized success response with translation.
    Consolidates duplicated success response patterns.

    Args:
        message: Success message template
        language: Response language for translation
        **format_args: Arguments for message formatting

    Returns:
        Translated success message
    """
    # Import here to avoid circular imports
    from app.utils.language import translate_response

    formatted_message = message.format(**format_args) if format_args else message
    return translate_response(formatted_message, language)
