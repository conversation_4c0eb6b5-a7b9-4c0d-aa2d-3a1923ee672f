# LangGraph Base React Agent

A minimal example demonstrating how to build a reactive agent using LangGraph. This agent serves as an excellent starting point for developers looking to implement LangGraph-based solutions.

## Key Features

- **Simple Architecture**: Shows the basic building blocks of a LangGraph agent
- **Streaming Support**: Includes streaming response capability using Vertex AI
- **Sample Tool Integration**: Includes a basic search tool to demonstrate tool usage
