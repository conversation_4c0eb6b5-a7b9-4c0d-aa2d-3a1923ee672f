"""
LLM Response Caching System for Performance Optimization.

This module provides caching capabilities for LLM responses to reduce API calls
and improve response times. Integrates with Task 2.1's exception framework.
"""

import hashlib
import json
import logging
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
from app.utils.exceptions import (
    ErrorContext, log_exception, sanitize_error_for_user
)

logger = logging.getLogger(__name__)


class LLMResponseCache:
    """
    Cache for LLM responses to reduce API calls and improve performance.

    Features:
    - Configurable TTL for cache entries
    - Intelligent cache key generation
    - Automatic cleanup of expired entries
    - Integration with Task 2.1 error handling
    """

    def __init__(self, ttl_minutes: int = 60, max_entries: int = 1000):
        """
        Initialize LLM response cache.

        Args:
            ttl_minutes: Time-to-live for cache entries in minutes
            max_entries: Maximum number of cache entries to maintain
        """
        self.cache: Dict[str, Dict] = {}
        self.ttl = timedelta(minutes=ttl_minutes)
        self.max_entries = max_entries
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'errors': 0
        }
        logger.info(f"LLM cache initialized with TTL={ttl_minutes}min, max_entries={max_entries}")

    def _generate_key(self, prompt: str, context: Dict) -> str:
        """
        Generate cache key from prompt and context.

        Args:
            prompt: LLM prompt text
            context: Context data for cache key generation

        Returns:
            MD5 hash of normalized prompt and context
        """
        try:
            # Normalize context to include only cacheable fields
            normalized_context = {
                'language': context.get('language', 'en'),
                'service_type': context.get('service_type'),
                'operation_type': context.get('operation_type', 'general')
            }

            # Create cache data structure
            cache_data = {
                "prompt_hash": hashlib.md5(prompt.encode()).hexdigest(),
                "context": normalized_context
            }

            # Generate cache key
            cache_key = json.dumps(cache_data, sort_keys=True)
            return hashlib.md5(cache_key.encode()).hexdigest()

        except Exception as e:
            # Use Task 2.1 error handling
            context_obj = ErrorContext(
                operation="cache_key_generation",
                additional_data={"prompt_length": len(prompt)}
            )
            log_exception(e, context=context_obj)
            self._stats['errors'] += 1
            # Return a fallback key to prevent cache failures
            return hashlib.md5(f"fallback_{prompt[:100]}".encode()).hexdigest()

    def get(self, prompt: str, context: Dict) -> Optional[str]:
        """
        Get cached response if available and not expired.

        Args:
            prompt: LLM prompt text
            context: Context data for cache lookup

        Returns:
            Cached response if available, None otherwise
        """
        try:
            key = self._generate_key(prompt, context)

            if key in self.cache:
                entry = self.cache[key]

                # Check if entry is still valid
                if datetime.utcnow() - entry['timestamp'] < self.ttl:
                    self._stats['hits'] += 1
                    logger.debug(f"Cache hit for key: {key[:8]}...")
                    return entry['response']
                else:
                    # Entry expired, remove it
                    del self.cache[key]
                    self._stats['evictions'] += 1
                    logger.debug(f"Cache entry expired and removed: {key[:8]}...")

            self._stats['misses'] += 1
            return None

        except Exception as e:
            # Use Task 2.1 error handling
            context_obj = ErrorContext(
                operation="cache_get",
                additional_data={"prompt_length": len(prompt)}
            )
            log_exception(e, context=context_obj)
            self._stats['errors'] += 1
            return None

    def set(self, prompt: str, context: Dict, response: str):
        """
        Cache LLM response with automatic cleanup.

        Args:
            prompt: LLM prompt text
            context: Context data for cache key generation
            response: LLM response to cache
        """
        try:
            key = self._generate_key(prompt, context)

            # Store cache entry
            self.cache[key] = {
                'response': response,
                'timestamp': datetime.utcnow(),
                'context': context.get('operation_type', 'general')
            }

            logger.debug(f"Cached response for key: {key[:8]}...")

            # Cleanup if cache is getting too large
            if len(self.cache) > self.max_entries:
                self._cleanup_oldest_entries()

        except Exception as e:
            # Use Task 2.1 error handling
            context_obj = ErrorContext(
                operation="cache_set",
                additional_data={
                    "prompt_length": len(prompt),
                    "response_length": len(response)
                }
            )
            log_exception(e, context=context_obj)
            self._stats['errors'] += 1

    def _cleanup_oldest_entries(self):
        """Remove oldest cache entries to maintain size limit."""
        try:
            # Sort entries by timestamp and remove oldest 20%
            entries_by_age = sorted(
                self.cache.items(),
                key=lambda x: x[1]['timestamp']
            )

            entries_to_remove = int(len(entries_by_age) * 0.2)
            for key, _ in entries_by_age[:entries_to_remove]:
                del self.cache[key]
                self._stats['evictions'] += 1

            logger.info(f"Cleaned up {entries_to_remove} old cache entries")

        except Exception as e:
            context_obj = ErrorContext(operation="cache_cleanup")
            log_exception(e, context=context_obj)
            self._stats['errors'] += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.

        Returns:
            Dictionary with cache performance metrics
        """
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_size': len(self.cache),
            'hit_rate_percent': round(hit_rate, 2),
            'total_hits': self._stats['hits'],
            'total_misses': self._stats['misses'],
            'total_evictions': self._stats['evictions'],
            'total_errors': self._stats['errors'],
            'max_entries': self.max_entries,
            'ttl_minutes': self.ttl.total_seconds() / 60
        }

    def clear(self):
        """Clear all cache entries."""
        self.cache.clear()
        logger.info("Cache cleared")


# Global cache instance for LLM responses
llm_cache = LLMResponseCache(ttl_minutes=60, max_entries=1000)


def get_cached_llm_response(prompt: str, context: Dict) -> Optional[str]:
    """
    Convenience function to get cached LLM response.

    Args:
        prompt: LLM prompt text
        context: Context data for cache lookup

    Returns:
        Cached response if available, None otherwise
    """
    return llm_cache.get(prompt, context)


def cache_llm_response(prompt: str, context: Dict, response: str):
    """
    Convenience function to cache LLM response.

    Args:
        prompt: LLM prompt text
        context: Context data for cache key generation
        response: LLM response to cache
    """
    llm_cache.set(prompt, context, response)


def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache performance statistics.

    Returns:
        Dictionary with cache performance metrics
    """
    return llm_cache.get_stats()


class PerformanceMonitor:
    """
    Monitor and collect performance metrics for optimization tracking.
    Integrates with Task 2.1's error handling framework.
    """

    def __init__(self):
        self._metrics = {
            'llm_calls': {'total': 0, 'cached': 0, 'duration_ms': []},
            'db_queries': {'total': 0, 'duration_ms': []},
            'tool_executions': {'total': 0, 'duration_ms': []},
            'errors': {'total': 0, 'cache_errors': 0, 'db_errors': 0}
        }
        self._start_time = datetime.utcnow()

    def record_llm_call(self, duration_ms: float, cached: bool = False):
        """Record LLM call performance metric."""
        self._metrics['llm_calls']['total'] += 1
        if cached:
            self._metrics['llm_calls']['cached'] += 1
        self._metrics['llm_calls']['duration_ms'].append(duration_ms)

        # Keep only last 100 measurements
        if len(self._metrics['llm_calls']['duration_ms']) > 100:
            self._metrics['llm_calls']['duration_ms'] = self._metrics['llm_calls']['duration_ms'][-100:]

    def record_db_query(self, duration_ms: float):
        """Record database query performance metric."""
        self._metrics['db_queries']['total'] += 1
        self._metrics['db_queries']['duration_ms'].append(duration_ms)

        # Keep only last 100 measurements
        if len(self._metrics['db_queries']['duration_ms']) > 100:
            self._metrics['db_queries']['duration_ms'] = self._metrics['db_queries']['duration_ms'][-100:]

    def record_tool_execution(self, duration_ms: float):
        """Record tool execution performance metric."""
        self._metrics['tool_executions']['total'] += 1
        self._metrics['tool_executions']['duration_ms'].append(duration_ms)

        # Keep only last 100 measurements
        if len(self._metrics['tool_executions']['duration_ms']) > 100:
            self._metrics['tool_executions']['duration_ms'] = self._metrics['tool_executions']['duration_ms'][-100:]

    def record_error(self, error_type: str = 'general'):
        """Record error occurrence."""
        self._metrics['errors']['total'] += 1
        if error_type in ['cache', 'db']:
            self._metrics['errors'][f'{error_type}_errors'] += 1

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        def avg(values):
            return sum(values) / len(values) if values else 0

        llm_calls = self._metrics['llm_calls']
        cache_hit_rate = (llm_calls['cached'] / llm_calls['total'] * 100) if llm_calls['total'] > 0 else 0

        return {
            'uptime_minutes': (datetime.utcnow() - self._start_time).total_seconds() / 60,
            'llm_performance': {
                'total_calls': llm_calls['total'],
                'cached_calls': llm_calls['cached'],
                'cache_hit_rate_percent': round(cache_hit_rate, 2),
                'avg_duration_ms': round(avg(llm_calls['duration_ms']), 2),
                'estimated_time_saved_ms': llm_calls['cached'] * 1000  # Assume 1s saved per cached call
            },
            'database_performance': {
                'total_queries': self._metrics['db_queries']['total'],
                'avg_duration_ms': round(avg(self._metrics['db_queries']['duration_ms']), 2)
            },
            'tool_performance': {
                'total_executions': self._metrics['tool_executions']['total'],
                'avg_duration_ms': round(avg(self._metrics['tool_executions']['duration_ms']), 2)
            },
            'error_metrics': self._metrics['errors'],
            'cache_stats': get_cache_stats()
        }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
