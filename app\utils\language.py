import os
import logging
from langchain_google_genai import ChatGoogleGenerativeAI
from app.prompts import TRANSLATE_PROMPT

logger = logging.getLogger(__name__)

def initialize_llm():
    """Initialize the language model for translations."""
    return ChatGoogleGenerativeAI(
        model="gemini-2.0-flash",
        google_api_key=os.getenv('GOOGLE_API_KEY'),
        temperature=0.7,
        max_tokens=1024
    )

def detect_language(text: str) -> str:
    """Detect if the input text is primarily Arabic or English based on Unicode script."""
    arabic_chars = sum(1 for c in text if '\u0600' <= c <= '\u06FF')
    total_chars = len(text.strip())
    if total_chars == 0:
        return "en"
    return "ar" if arabic_chars / total_chars > 0.5 else "en"

def translate_response(response: str, language: str) -> str:
    """Translate an English response to Arabic if language is 'ar'."""
    if language != "ar":
        return response
    try:
        llm = initialize_llm()
        prompt = TRANSLATE_PROMPT.format(response=response)
        translated = llm.invoke(prompt)
        translated_response = translated.content.strip()
        logger.debug(f"Translated response: {translated_response}")
        return translated_response
    except Exception as e:
        logger.error(f"Error translating response: {str(e)}")
        return response  # Fallback to English if translation fails