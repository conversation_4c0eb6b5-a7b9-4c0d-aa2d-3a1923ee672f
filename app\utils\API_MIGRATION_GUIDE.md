# Odoo API Migration Guide

## Overview

This document describes the migration from XML-RPC Odoo integration to the new REST API for Odoo.sh hosted instances.

## What Changed

### Before (XML-RPC Integration)
- Direct XML-RPC calls to local/self-hosted Odoo instance
- Authentication via username/password
- Creates partners, leads, and calendar events
- Uses `res.partner`, `crm.lead`, and `calendar.event` models

### After (REST API Integration)
- HTTP REST API calls to Odoo.sh hosted instance
- Authentication via API key
- Creates customers and bookings
- Uses `/api/customer` and `/api/booking` endpoints

## Configuration

### Environment Variables

Add these new variables to your `.env` file:

```bash
# Odoo.sh REST API Configuration (New)
ODOO_API_BASE_URL=https://your-instance.odoo.sh
ODOO_API_KEY=your_api_key_here
```

### Automatic Detection

The system automatically detects which configuration is available:
1. **REST API First**: If `ODOO_API_BASE_URL` and `ODOO_API_KEY` are set, uses REST API
2. **XML-RPC Fallback**: If REST API config is missing, falls back to XML-RPC
3. **Error**: If neither configuration is complete, raises an error

## Data Mapping

### Appointment → Booking Conversion

| Appointment Field | Booking Field | Notes |
|------------------|---------------|-------|
| `client_name` | `customer_id` | Lookup/create customer first |
| `phone_number` | `customer.phone` | Used for customer lookup |
| `service_type` | `service_id` | Mapped via service lookup |
| `appointment_date` | `booking_date` | Direct mapping |
| `appointment_time` | `start_time` | Combined with date |
| `duration` | `end_time` | Calculated from start + duration |
| `worker_id` | `worker_id` | Direct mapping or default |

### Customer Creation

When processing appointments, the system:
1. Searches for existing customer by phone number
2. If found, uses existing customer ID
3. If not found, creates new customer with appointment data

## API Methods

### New REST API Methods

The `OdooRestAPI` class provides these methods:

#### Bookings
- `get_bookings(customer_id, worker_id, state)` - List bookings with filters
- `get_booking(booking_id)` - Get specific booking
- `create_booking(booking_data)` - Create new booking
- `update_booking(booking_id, data)` - Update booking
- `confirm_booking(booking_id)` - Confirm booking
- `cancel_booking(booking_id)` - Cancel booking

#### Customers
- `get_customers(name, email, phone)` - List customers with filters
- `get_customer(customer_id)` - Get specific customer
- `create_customer(customer_data)` - Create new customer
- `update_customer(customer_id, data)` - Update customer

#### Workers
- `get_workers(name, region_id, availability_status, skill_id)` - List workers
- `get_worker(worker_id)` - Get specific worker

## Backward Compatibility

### Interface Preservation

The main `OdooIntegration` class maintains the same public interface:
- `process_appointments()` - Still works with both APIs
- All existing methods remain available
- No changes needed in `app/agent.py` or `app/tools.py`

### Graceful Degradation

If the REST API is unavailable:
1. System automatically falls back to XML-RPC
2. Logs indicate which integration is being used
3. Error handling provides clear messages

## Testing

### Test Script

Run the migration test script:

```bash
python app/test_api_migration.py
```

This tests:
- Configuration detection
- Appointment creation
- Data mapping
- API methods (if configured)
- Appointment processing

### Manual Testing

1. **Test Current Setup**: Ensure existing XML-RPC integration still works
2. **Add API Config**: Add REST API environment variables
3. **Test Migration**: Run test script to verify new API works
4. **Compare Results**: Verify both approaches produce similar results

## Migration Steps

### Phase 1: Preparation
1. ✅ Implement REST API client
2. ✅ Add data mapping functions
3. ✅ Create test scripts
4. ✅ Update documentation

### Phase 2: Testing (Current Phase)
1. 🔄 Test with development API key
2. 🔄 Validate appointment → booking conversion
3. 🔄 Compare XML-RPC vs REST API results
4. 🔄 Test error handling and fallback

### Phase 3: Production Migration
1. ⏳ Obtain production API key
2. ⏳ Update environment variables
3. ⏳ Monitor system behavior
4. ⏳ Remove XML-RPC configuration (optional)

## Error Handling

### API-Specific Errors

The system handles these API-specific scenarios:
- **401 Unauthorized**: Invalid or missing API key
- **404 Not Found**: Resource doesn't exist
- **400 Bad Request**: Invalid data format
- **500 Server Error**: Odoo.sh server issues

### Fallback Behavior

If REST API fails:
1. Logs the error with context
2. Falls back to XML-RPC if available
3. Provides user-friendly error messages
4. Continues processing other appointments

## Security Considerations

### API Key Management
- Store API key securely in environment variables
- Never commit API keys to version control
- Use different keys for development/production
- Rotate keys regularly

### HTTPS Enforcement
- All API calls use HTTPS
- Certificate validation enabled
- Timeout protection (30 seconds)

## Performance Notes

### REST API Benefits
- Faster than XML-RPC for simple operations
- Better error handling and status codes
- Standardized JSON responses
- Built for Odoo.sh infrastructure

### Considerations
- Network latency for external API calls
- Rate limiting (if implemented by Odoo.sh)
- Retry logic for transient failures

## Support

### Troubleshooting

1. **Check Logs**: Look for integration type and error messages
2. **Verify Config**: Ensure environment variables are correct
3. **Test Connection**: Use test script to validate setup
4. **API Documentation**: Refer to `app/utils/README.md` for API details

### Common Issues

- **Missing API Key**: Add `ODOO_API_KEY` to environment
- **Wrong Base URL**: Ensure `ODOO_API_BASE_URL` includes your instance
- **Service/Worker IDs**: Update default mappings in code
- **Data Format**: Check appointment data structure
