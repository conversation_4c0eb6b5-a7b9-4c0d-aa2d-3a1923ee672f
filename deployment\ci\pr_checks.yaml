# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

steps:
  # Install uv package manager and sync dependencies
  - name: "python:3.11-slim"
    id: install-dependencies
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        pip install uv==0.6.12 --user && uv sync --frozen
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

  # Run unit tests using pytest
  - name: "python:3.11-slim"
    id: unit-tests
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        uv run pytest tests/unit
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

  # Run integration tests
  - name: "python:3.11-slim"
    id: integration-tests
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        uv run pytest tests/integration
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

logsBucket: gs://${PROJECT_ID}-my-agent-logs-data/build-logs
options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
