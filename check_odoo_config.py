#!/usr/bin/env python3
"""
Odoo Configuration Diagnostic Tool

This script helps diagnose Odoo integration configuration issues.
Run this to check if your Odoo environment variables are properly set.
"""

import os
from dotenv import load_dotenv

def check_odoo_config():
    """Check Odoo configuration and provide diagnostic information."""
    print("🔍 Odoo Configuration Diagnostic Tool")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check REST API configuration
    print("\n📡 REST API Configuration:")
    rest_api_base_url = os.getenv('ODOO_API_BASE_URL')
    rest_api_key = os.getenv('ODOO_API_KEY')
    
    if rest_api_base_url and rest_api_key:
        print(f"✅ ODOO_API_BASE_URL: {rest_api_base_url}")
        print(f"✅ ODOO_API_KEY: {'*' * (len(rest_api_key) - 4) + rest_api_key[-4:] if len(rest_api_key) > 4 else '***'}")
        print("✅ REST API configuration is available")
    else:
        print("❌ REST API configuration missing:")
        if not rest_api_base_url:
            print("   - ODOO_API_BASE_URL not set")
        if not rest_api_key:
            print("   - ODOO_API_KEY not set")
    
    # Check XML-RPC configuration
    print("\n🔗 XML-RPC Configuration:")
    odoo_url = os.getenv('ODOO_URL')
    odoo_db = os.getenv('ODOO_DB')
    odoo_username = os.getenv('ODOO_USERNAME')
    odoo_password = os.getenv('ODOO_PASSWORD')
    
    if odoo_url and odoo_db and odoo_username and odoo_password:
        print(f"✅ ODOO_URL: {odoo_url}")
        print(f"✅ ODOO_DB: {odoo_db}")
        print(f"✅ ODOO_USERNAME: {odoo_username}")
        print(f"✅ ODOO_PASSWORD: {'*' * (len(odoo_password) - 2) + odoo_password[-2:] if len(odoo_password) > 2 else '***'}")
        print("✅ XML-RPC configuration is available")
    else:
        print("❌ XML-RPC configuration missing:")
        if not odoo_url:
            print("   - ODOO_URL not set")
        if not odoo_db:
            print("   - ODOO_DB not set")
        if not odoo_username:
            print("   - ODOO_USERNAME not set")
        if not odoo_password:
            print("   - ODOO_PASSWORD not set")
    
    # Provide recommendations
    print("\n💡 Recommendations:")
    
    if rest_api_base_url and rest_api_key:
        print("✅ Use REST API integration (recommended for hosted Odoo)")
        print("   Your application will use the modern REST API")
    elif odoo_url and odoo_db and odoo_username and odoo_password:
        print("✅ Use XML-RPC integration (fallback option)")
        print("   Your application will use the traditional XML-RPC API")
    else:
        print("❌ No valid Odoo configuration found!")
        print("\n🔧 To fix this, set one of these configurations:")
        print("\n   Option 1 - REST API (Recommended for hosted Odoo):")
        print("   export ODOO_API_BASE_URL='https://your-odoo-instance.com'")
        print("   export ODOO_API_KEY='your-api-key'")
        print("\n   Option 2 - XML-RPC (Traditional):")
        print("   export ODOO_URL='https://your-odoo-instance.com'")
        print("   export ODOO_DB='your-database-name'")
        print("   export ODOO_USERNAME='your-username'")
        print("   export ODOO_PASSWORD='your-password'")
    
    # Test connection if configuration is available
    if (rest_api_base_url and rest_api_key) or (odoo_url and odoo_db and odoo_username and odoo_password):
        print("\n🧪 Testing Connection...")
        try:
            from app.utils.odoo_integration import OdooIntegration
            odoo = OdooIntegration()
            print("✅ Odoo integration initialized successfully!")
            
            if odoo.use_rest_api:
                print("✅ Using REST API integration")
            else:
                print("✅ Using XML-RPC integration")
                
        except Exception as e:
            print(f"❌ Connection test failed: {str(e)}")
            print("\n🔧 Troubleshooting tips:")
            print("   1. Check that your Odoo instance is accessible")
            print("   2. Verify your credentials are correct")
            print("   3. Ensure your Odoo instance supports the API you're trying to use")
            print("   4. Check firewall/network connectivity")
    
    print("\n" + "=" * 50)
    print("Diagnostic complete!")

if __name__ == "__main__":
    check_odoo_config()
