import os
import logging
from datetime import datetime, timedelta
import json
from app.utils.database import Database<PERSON>anager, Appointment, Worker, WorkerPreference
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_new_columns():
    """Test the new location and serial_number columns."""
    try:
        # Initialize database
        db = DatabaseManager()
        logger.info("Successfully connected to database")
        
        # Create test appointment
        test_appointment = {
            'client_name': 'Test Client',
            'phone_number': '1234567880',
            'service_type': 'Haircut',
            'appointment_date': datetime.now().date() + timedelta(days=1),
            'appointment_time': '14:00',
            'location': 'Downtown Branch'
        }
        
        # Save appointment
        appointment_id = db.save_appointment(test_appointment)
        logger.info(f"Created test appointment with ID: {appointment_id}")
        
        # Retrieve and verify appointment
        query = """
            SELECT * FROM appointments 
            WHERE id = %s
        """
        result = db.execute_query(query, (appointment_id,))
        
        if result:
            appointment = result[0]
            logger.info("\n=== Appointment Details ===")
            logger.info(f"ID: {appointment['id']}")
            logger.info(f"Client Name: {appointment['client_name']}")
            logger.info(f"Location: {appointment['location']}")
            logger.info(f"Serial Number: {appointment['serial_number']}")
            
            # Verify location
            assert appointment['location'] == 'Downtown Branch', "Location not saved correctly"
            
            # Verify serial number format
            assert appointment['serial_number'].startswith('APPT-'), "Serial number format incorrect"
            assert len(appointment['serial_number']) == 11, "Serial number length incorrect"
            
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Failed to retrieve test appointment")
            
        # Clean up
        with db.get_cursor() as cur:
            cur.execute("DELETE FROM appointments WHERE id = %s", (appointment_id,))
        logger.info("Test data cleaned up")
        
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        raise

def test_worker_management():
    """Test worker creation and management."""
    try:
        db = DatabaseManager()
        logger.info("Testing worker management...")
        
        # Create test worker
        worker = Worker(
            name="Test Worker",
            specialization="Massage"
        )
        
        # Save worker
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO workers (name, specialization)
                VALUES (%s, %s)
                RETURNING id
            """, (worker.name, worker.specialization))
            worker_id = cur.fetchone()[0]
            logger.info(f"Created test worker with ID: {worker_id}")
        
        # Verify worker
        with db.get_cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM workers WHERE id = %s", (worker_id,))
            result = cur.fetchone()
            
            assert result['name'] == worker.name, "Worker name not saved correctly"
            assert result['specialization'] == worker.specialization, "Worker specialization not saved correctly"
            
            logger.info("✅ Worker management tests passed!")
        
        # Clean up
        with db.get_cursor() as cur:
            cur.execute("DELETE FROM workers WHERE id = %s", (worker_id,))
        logger.info("Test data cleaned up")
        
    except Exception as e:
        logger.error(f"Error during worker test: {str(e)}")
        raise

def test_worker_preferences():
    """Test worker preference management."""
    try:
        db = DatabaseManager()
        logger.info("Testing worker preferences...")
        
        # Create test client
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO clients (client_name, phone_number)
                VALUES (%s, %s)
                RETURNING id
            """, ("Test Client", "1234567880"))
            client_id = cur.fetchone()[0]
        
        # Create test worker
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO workers (name)
                VALUES (%s)
                RETURNING id
            """, ("Test Worker",))
            worker_id = cur.fetchone()[0]
        
        # Create preference
        preference = WorkerPreference(
            client_id=client_id,
            worker_id=worker_id,
            preference_type="preferred"
        )
        
        # Save preference
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO worker_preferences (client_id, worker_id, preference_type)
                VALUES (%s, %s, %s)
                RETURNING id
            """, (preference.client_id, preference.worker_id, preference.preference_type))
            preference_id = cur.fetchone()[0]
            logger.info(f"Created test preference with ID: {preference_id}")
        
        # Verify preference
        with db.get_cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM worker_preferences WHERE id = %s", (preference_id,))
            result = cur.fetchone()
            
            assert result['client_id'] == preference.client_id, "Client ID not saved correctly"
            assert result['worker_id'] == preference.worker_id, "Worker ID not saved correctly"
            assert result['preference_type'] == preference.preference_type, "Preference type not saved correctly"
            
            logger.info("✅ Worker preference tests passed!")
        
        # Clean up
        with db.get_cursor() as cur:
            cur.execute("DELETE FROM worker_preferences WHERE id = %s", (preference_id,))
            cur.execute("DELETE FROM workers WHERE id = %s", (worker_id,))
            cur.execute("DELETE FROM clients WHERE id = %s", (client_id,))
        logger.info("Test data cleaned up")
        
    except Exception as e:
        logger.error(f"Error during preference test: {str(e)}")
        raise

def test_appointment_with_new_fields():
    """Test appointment creation with new fields."""
    try:
        db = DatabaseManager()
        logger.info("Testing appointment with new fields...")
        
        # Create test worker
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO workers (name)
                VALUES (%s)
                RETURNING id
            """, ("Test Worker",))
            worker_id = cur.fetchone()[0]
        
        # Create test appointment with new fields
        recurrence_pattern = {
            'frequency': 'weekly',
            'interval': 1
        }
        
        test_appointment = {
            'client_name': 'Test Client',
            'phone_number': '1234567890',
            'service_type': 'Massage',
            'appointment_date': datetime.now().date() + timedelta(days=1),
            'appointment_time': '14:00',
            'location': 'Downtown Branch',
            'duration': 120,  # 2 hours
            'is_recurring': True,
            'recurrence_pattern': json.dumps(recurrence_pattern),
            'recurrence_end_date': datetime.now().date() + timedelta(days=30),
            'worker_id': worker_id
        }
        
        # Save appointment
        with db.get_cursor() as cur:
            cur.execute("""
                INSERT INTO appointments 
                (client_name, phone_number, service_type, appointment_date, appointment_time,
                 location, serial_number, duration, is_recurring, recurrence_pattern,
                 recurrence_end_date, worker_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (
                test_appointment['client_name'],
                test_appointment['phone_number'],
                test_appointment['service_type'],
                test_appointment['appointment_date'],
                test_appointment['appointment_time'],
                test_appointment['location'],
                f"APPT-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                test_appointment['duration'],
                test_appointment['is_recurring'],
                test_appointment['recurrence_pattern'],
                test_appointment['recurrence_end_date'],
                test_appointment['worker_id']
            ))
            appointment_id = cur.fetchone()[0]
            logger.info(f"Created test appointment with ID: {appointment_id}")
        
        # Verify appointment
        with db.get_cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM appointments WHERE id = %s", (appointment_id,))
            result = cur.fetchone()
            
            assert result['duration'] == test_appointment['duration'], "Duration not saved correctly"
            assert result['is_recurring'] == test_appointment['is_recurring'], "Is recurring not saved correctly"
            assert result['worker_id'] == test_appointment['worker_id'], "Worker ID not saved correctly"
            
            # Verify recurrence pattern
            stored_pattern = json.loads(result['recurrence_pattern'])
            expected_pattern = json.loads(test_appointment['recurrence_pattern'])
            assert stored_pattern == expected_pattern, "Recurrence pattern not saved correctly"
            
            logger.info("✅ Appointment with new fields tests passed!")
        
        # Clean up
        with db.get_cursor() as cur:
            cur.execute("DELETE FROM appointments WHERE id = %s", (appointment_id,))
            cur.execute("DELETE FROM workers WHERE id = %s", (worker_id,))
        logger.info("Test data cleaned up")
        
    except Exception as e:
        logger.error(f"Error during appointment test: {str(e)}")
        raise

if __name__ == "__main__":
    test_new_columns()
    test_worker_management()
    test_worker_preferences()
    test_appointment_with_new_fields() 