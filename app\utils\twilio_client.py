from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from typing import Op<PERSON>, Dict, <PERSON><PERSON>, BinaryIO
import os
import logging
import tempfile
import requests
import hashlib
from pathlib import Path
from contextlib import contextmanager
from dataclasses import dataclass
from app.utils.exceptions import (
    ExternalAPIException, ErrorContext, handle_external_api_error, log_exception
)
from app.utils.validation import MAX_AUDIO_FILE_SIZE_MB, SUPPORTED_AUDIO_FORMATS

logger = logging.getLogger(__name__)

# Media download constants
MAX_DOWNLOAD_SIZE_BYTES = MAX_AUDIO_FILE_SIZE_MB * 1024 * 1024  # 16MB in bytes
DOWNLOAD_TIMEOUT_SECONDS = 30  # Timeout for media downloads
MAX_RETRY_ATTEMPTS = 3  # Maximum retry attempts for failed downloads
CHUNK_SIZE = 8192  # Download chunk size in bytes

@dataclass
class MediaDownloadResult:
    """Result of a media download operation."""
    success: bool
    file_path: Optional[str] = None
    content_type: Optional[str] = None
    file_size: int = 0
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None

class TwilioClient:
    def __init__(self):
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        self.client = Client(account_sid, auth_token)
        self.whatsapp_number = os.getenv('TWILIO_WHATSAPP_NUMBER')

    def send_message(self, to_number: str, message: str) -> Optional[str]:
        """
        Send a WhatsApp message to a phone number.
        Returns the message SID if successful, None otherwise.
        """
        try:
            # Ensure the number has whatsapp: prefix
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'

            # Validate phone number format
            if not self.validate_phone_number(to_number):
                validation_exception = ExternalAPIException(
                    message=f"Invalid phone number format: {to_number}",
                    service="Twilio",
                    user_message="Invalid phone number format",
                    context=ErrorContext(
                        operation="validate_phone_number",
                        additional_data={"phone_number": to_number[:10] + "..."}
                    )
                )
                log_exception(validation_exception)
                return None

            message = self.client.messages.create(
                from_=f'whatsapp:{self.whatsapp_number}',
                body=message,
                to=to_number
            )
            return message.sid
        except Exception as e:
            # Use standardized external API error handling
            api_exception = handle_external_api_error(
                service="Twilio",
                operation="send_message",
                original_exception=e,
                context=ErrorContext(
                    operation="send_whatsapp_message",
                    additional_data={"to_number": to_number[:10] + "..."}
                )
            )
            log_exception(api_exception)
            return None

    def create_response(self, message: str) -> str:
        """
        Create a TwiML response for incoming messages.
        """
        response = MessagingResponse()
        response.message(message)
        return str(response)

    def validate_phone_number(self, phone_number: str) -> bool:
        """
        Validate if a phone number is in a valid format for WhatsApp.
        Returns True if valid, False otherwise.
        """
        try:
            # Check if it's a WhatsApp number
            if not phone_number.startswith('whatsapp:'):
                return False

            # Remove whatsapp: prefix and any non-digit characters
            cleaned_number = ''.join(filter(str.isdigit, phone_number[9:]))

            # Basic validation - phone numbers should be between 10 and 15 digits
            return 10 <= len(cleaned_number) <= 15

        except Exception as e:
            # Log validation errors but don't raise exceptions for validation
            context = ErrorContext(
                operation="validate_phone_number",
                additional_data={"phone_number": phone_number[:10] + "..."}
            )
            log_exception(e, context=context)
            return False

    @contextmanager
    def _secure_temp_file(self, suffix: str = ".tmp", prefix: str = "twilio_media_"):
        """
        Create a secure temporary file with automatic cleanup.

        Args:
            suffix: File suffix/extension
            prefix: File prefix for identification

        Yields:
            Tuple of (file_path, file_handle)
        """
        temp_file = None
        try:
            # Create temporary file with secure permissions (600 - owner read/write only)
            temp_file = tempfile.NamedTemporaryFile(
                mode='wb',
                suffix=suffix,
                prefix=prefix,
                delete=False  # We'll handle deletion manually for better control
            )

            # Set secure file permissions (owner read/write only)
            os.chmod(temp_file.name, 0o600)

            logger.debug(f"Created secure temporary file: {temp_file.name}")
            yield temp_file.name, temp_file

        except Exception as e:
            logger.error(f"Failed to create secure temporary file: {e}")
            raise
        finally:
            # Ensure file is closed and cleaned up
            if temp_file:
                try:
                    temp_file.close()
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                        logger.debug(f"Cleaned up temporary file: {temp_file.name}")
                except Exception as cleanup_error:
                    logger.error(f"Failed to cleanup temporary file {temp_file.name}: {cleanup_error}")

    def _validate_media_url(self, media_url: str) -> bool:
        """
        Validate that the media URL is a legitimate Twilio media URL.

        Args:
            media_url: URL to validate

        Returns:
            True if valid, False otherwise
        """
        if not media_url or not isinstance(media_url, str):
            return False

        # Basic URL structure validation
        if not media_url.startswith('https://api.twilio.com/'):
            logger.warning(f"Invalid media URL format: {media_url[:50]}...")
            return False

        # Check for required path components
        if '/Media/' not in media_url:
            logger.warning(f"Media URL missing required path components: {media_url[:50]}...")
            return False

        # Prevent directory traversal attacks
        if '..' in media_url or '//' in media_url.replace('https://', ''):
            logger.warning(f"Potential directory traversal in media URL: {media_url[:50]}...")
            return False

        return True

    def _validate_audio_content(self, content_type: str, file_size: int, file_path: str) -> Tuple[bool, str]:
        """
        Validate downloaded audio content.

        Args:
            content_type: MIME type of the content
            file_size: Size of the downloaded file
            file_path: Path to the downloaded file

        Returns:
            Tuple of (is_valid, error_message)
        """
        # Validate content type
        if content_type not in SUPPORTED_AUDIO_FORMATS:
            return False, f"Unsupported audio format: {content_type}"

        # Validate file size
        if file_size > MAX_DOWNLOAD_SIZE_BYTES:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed ({MAX_DOWNLOAD_SIZE_BYTES} bytes)"

        if file_size == 0:
            return False, "Downloaded file is empty"

        # Basic file header validation for common audio formats
        try:
            with open(file_path, 'rb') as f:
                header = f.read(12)  # Read first 12 bytes for format detection

            # Validate file headers for common formats
            if content_type == 'audio/ogg' and not header.startswith(b'OggS'):
                return False, "File header doesn't match OGG format"
            elif content_type == 'audio/mp3' and not (header.startswith(b'ID3') or header[0:2] == b'\xff\xfb'):
                return False, "File header doesn't match MP3 format"
            elif content_type == 'audio/mp4' and b'ftyp' not in header:
                return False, "File header doesn't match MP4 format"

        except Exception as e:
            logger.warning(f"Could not validate file header: {e}")
            # Don't fail validation for header check errors, just log them

        return True, ""

    def download_media(self, media_url: str, expected_content_type: str = None) -> MediaDownloadResult:
        """
        Download audio media from Twilio MediaUrl with comprehensive security and validation.

        Args:
            media_url: Twilio media URL to download from
            expected_content_type: Expected MIME type for validation

        Returns:
            MediaDownloadResult with download status and file information
        """
        # Initialize result object
        result = MediaDownloadResult(success=False)

        try:
            # Validate media URL format and security
            if not self._validate_media_url(media_url):
                result.error_message = "Invalid or potentially unsafe media URL"
                logger.warning(f"Media download rejected - invalid URL: {media_url[:50]}...")
                return result

            # Log download attempt
            logger.info(f"Starting media download from: {media_url[:50]}...")

            # Attempt download with retry logic
            for attempt in range(1, MAX_RETRY_ATTEMPTS + 1):
                try:
                    result = self._attempt_media_download(media_url, expected_content_type, attempt)
                    if result.success:
                        logger.info(f"Media download successful on attempt {attempt}: "
                                  f"size={result.file_size} bytes, type={result.content_type}")
                        return result
                    elif attempt < MAX_RETRY_ATTEMPTS:
                        logger.warning(f"Media download attempt {attempt} failed: {result.error_message}. Retrying...")
                        continue
                    else:
                        logger.error(f"Media download failed after {MAX_RETRY_ATTEMPTS} attempts: {result.error_message}")
                        return result

                except Exception as e:
                    error_msg = f"Download attempt {attempt} failed with exception: {str(e)}"
                    logger.error(error_msg)

                    if attempt < MAX_RETRY_ATTEMPTS:
                        continue
                    else:
                        # Final attempt failed
                        api_exception = handle_external_api_error(
                            service="Twilio",
                            operation="download_media",
                            original_exception=e,
                            context=ErrorContext(
                                operation="media_download",
                                additional_data={
                                    "media_url": media_url[:50] + "...",
                                    "attempt": attempt,
                                    "expected_content_type": expected_content_type
                                }
                            )
                        )
                        log_exception(api_exception)
                        result.error_message = f"Download failed after {MAX_RETRY_ATTEMPTS} attempts: {str(e)}"
                        return result

        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unexpected error in media download: {e}")
            result.error_message = f"Unexpected download error: {str(e)}"
            return result

    def _attempt_media_download(self, media_url: str, expected_content_type: str, attempt: int) -> MediaDownloadResult:
        """
        Attempt a single media download with full validation and security checks.

        Args:
            media_url: Twilio media URL to download from
            expected_content_type: Expected MIME type for validation
            attempt: Current attempt number (for logging)

        Returns:
            MediaDownloadResult with download status and file information
        """
        result = MediaDownloadResult(success=False)

        # Prepare authentication for Twilio API
        auth = (os.getenv('TWILIO_ACCOUNT_SID'), os.getenv('TWILIO_AUTH_TOKEN'))

        # Configure request headers
        headers = {
            'User-Agent': 'WhatsApp-Booking-Agent/1.0',
            'Accept': 'audio/*'
        }

        try:
            # Make HTTP request with timeout and streaming
            response = requests.get(
                media_url,
                auth=auth,
                headers=headers,
                timeout=DOWNLOAD_TIMEOUT_SECONDS,
                stream=True  # Stream download for large files
            )

            # Check HTTP status
            if response.status_code == 404:
                result.error_message = "Media file not found (404)"
                return result
            elif response.status_code == 403:
                result.error_message = "Access denied to media file (403)"
                return result
            elif response.status_code != 200:
                result.error_message = f"HTTP error {response.status_code}: {response.reason}"
                return result

            # Get content information from headers
            content_type = response.headers.get('content-type', '').lower()
            content_length = response.headers.get('content-length')

            # Validate content type
            if expected_content_type and content_type != expected_content_type.lower():
                result.error_message = f"Content type mismatch: expected {expected_content_type}, got {content_type}"
                return result

            if content_type not in [fmt.lower() for fmt in SUPPORTED_AUDIO_FORMATS]:
                result.error_message = f"Unsupported content type: {content_type}"
                return result

            # Validate content length if provided
            if content_length:
                try:
                    size = int(content_length)
                    if size > MAX_DOWNLOAD_SIZE_BYTES:
                        result.error_message = f"File too large: {size} bytes (max: {MAX_DOWNLOAD_SIZE_BYTES})"
                        return result
                except ValueError:
                    logger.warning(f"Invalid content-length header: {content_length}")

            # Download file to secure temporary location
            with self._secure_temp_file(suffix=self._get_file_extension(content_type)) as (temp_path, temp_file):
                downloaded_size = 0
                file_hash = hashlib.sha256()

                # Download in chunks with size monitoring
                for chunk in response.iter_content(chunk_size=CHUNK_SIZE):
                    if chunk:  # Filter out keep-alive chunks
                        downloaded_size += len(chunk)

                        # Check size limit during download
                        if downloaded_size > MAX_DOWNLOAD_SIZE_BYTES:
                            result.error_message = f"Download exceeded size limit during transfer: {downloaded_size} bytes"
                            return result

                        temp_file.write(chunk)
                        file_hash.update(chunk)

                temp_file.flush()

                # Final validation
                is_valid, validation_error = self._validate_audio_content(content_type, downloaded_size, temp_path)
                if not is_valid:
                    result.error_message = f"Audio validation failed: {validation_error}"
                    return result

                # Create metadata
                metadata = {
                    'file_hash': file_hash.hexdigest(),
                    'download_attempt': attempt,
                    'content_length_header': content_length,
                    'actual_size': downloaded_size,
                    'headers': dict(response.headers)
                }

                # Success - populate result
                result.success = True
                result.file_path = temp_path
                result.content_type = content_type
                result.file_size = downloaded_size
                result.metadata = metadata

                return result

        except requests.exceptions.Timeout:
            result.error_message = f"Download timeout after {DOWNLOAD_TIMEOUT_SECONDS} seconds"
            return result
        except requests.exceptions.ConnectionError:
            result.error_message = "Network connection error during download"
            return result
        except requests.exceptions.RequestException as e:
            result.error_message = f"HTTP request failed: {str(e)}"
            return result
        except Exception as e:
            result.error_message = f"Unexpected error during download: {str(e)}"
            return result

    def _get_file_extension(self, content_type: str) -> str:
        """
        Get appropriate file extension for content type.

        Args:
            content_type: MIME type

        Returns:
            File extension including dot
        """
        extension_map = {
            'audio/ogg': '.ogg',
            'audio/mpeg': '.mp3',
            'audio/mp3': '.mp3',
            'audio/mp4': '.mp4',
            'audio/3gpp': '.3gp',
            'audio/amr': '.amr',
            'audio/amr-nb': '.amr',
            'audio/webm': '.webm'
        }
        return extension_map.get(content_type.lower(), '.audio')