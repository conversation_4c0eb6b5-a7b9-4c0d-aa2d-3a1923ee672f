from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from typing import Optional
import os
import logging
from app.utils.exceptions import (
    ExternalAPIException, ErrorContext, handle_external_api_error, log_exception
)

logger = logging.getLogger(__name__)

class TwilioClient:
    def __init__(self):
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        self.client = Client(account_sid, auth_token)
        self.whatsapp_number = os.getenv('TWILIO_WHATSAPP_NUMBER')

    def send_message(self, to_number: str, message: str) -> Optional[str]:
        """
        Send a WhatsApp message to a phone number.
        Returns the message SID if successful, None otherwise.
        """
        try:
            # Ensure the number has whatsapp: prefix
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'

            # Validate phone number format
            if not self.validate_phone_number(to_number):
                validation_exception = ExternalAPIException(
                    message=f"Invalid phone number format: {to_number}",
                    service="Twilio",
                    user_message="Invalid phone number format",
                    context=ErrorContext(
                        operation="validate_phone_number",
                        additional_data={"phone_number": to_number[:10] + "..."}
                    )
                )
                log_exception(validation_exception)
                return None

            message = self.client.messages.create(
                from_=f'whatsapp:{self.whatsapp_number}',
                body=message,
                to=to_number
            )
            return message.sid
        except Exception as e:
            # Use standardized external API error handling
            api_exception = handle_external_api_error(
                service="Twilio",
                operation="send_message",
                original_exception=e,
                context=ErrorContext(
                    operation="send_whatsapp_message",
                    additional_data={"to_number": to_number[:10] + "..."}
                )
            )
            log_exception(api_exception)
            return None

    def create_response(self, message: str) -> str:
        """
        Create a TwiML response for incoming messages.
        """
        response = MessagingResponse()
        response.message(message)
        return str(response)

    def validate_phone_number(self, phone_number: str) -> bool:
        """
        Validate if a phone number is in a valid format for WhatsApp.
        Returns True if valid, False otherwise.
        """
        try:
            # Check if it's a WhatsApp number
            if not phone_number.startswith('whatsapp:'):
                return False

            # Remove whatsapp: prefix and any non-digit characters
            cleaned_number = ''.join(filter(str.isdigit, phone_number[9:]))

            # Basic validation - phone numbers should be between 10 and 15 digits
            return 10 <= len(cleaned_number) <= 15

        except Exception as e:
            # Log validation errors but don't raise exceptions for validation
            context = ErrorContext(
                operation="validate_phone_number",
                additional_data={"phone_number": phone_number[:10] + "..."}
            )
            log_exception(e, context=context)
            return False