import os
import psycopg2
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ClientManagement:
    def __init__(self):
        self.db_settings = {
            "dbname": os.getenv("DB_NAME", "beauty_center"),
            "user": os.getenv("DB_USER"),
            "password": os.getenv("DB_PASSWORD"),
            "host": os.getenv("DB_HOST"),
            "port": os.getenv("DB_PORT")
        }
        self.init_db()

    def init_db(self):
        """Initialize the clients table if it doesn't exist."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                # Create clients table
                cur.execute("""
                    CREATE TABLE IF NOT EXISTS clients (
                        id SERIAL PRIMARY KEY,
                        client_name VARCHAR(255) NOT NULL,
                        phone_number VARCHAR(20) UNIQUE NOT NULL,
                        location VARCHAR(255),
                        contract_type VARCHAR(50) DEFAULT 'one-time',
                        service_type TEXT,
                        most_asked_provider VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)
                
                # Create index for faster lookups
                cur.execute("""
                    CREATE INDEX IF NOT EXISTS idx_clients_phone_number 
                    ON clients (phone_number);
                """)
                
                conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error initializing clients table: {str(e)}")

    def get_client_by_phone(self, phone_number: str) -> Optional[Dict]:
        """Get client by phone number."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT id, client_name, phone_number, location, 
                           contract_type, service_type, most_asked_provider
                    FROM clients
                    WHERE phone_number = %s
                """, (phone_number,))
                row = cur.fetchone()
                if row:
                    return {
                        "id": row[0],
                        "client_name": row[1],
                        "phone_number": row[2],
                        "location": row[3],
                        "contract_type": row[4],
                        "service_type": row[5],
                        "most_asked_provider": row[6]
                    }
                return None
        except Exception as e:
            print(f"Error getting client: {str(e)}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()

    def create_client(self, client_data: Dict) -> Optional[int]:
        """Create a new client record."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO clients 
                    (client_name, phone_number, location, contract_type, 
                     service_type, most_asked_provider)
                    VALUES (%(client_name)s, %(phone_number)s, %(location)s, 
                            %(contract_type)s, %(service_type)s, %(most_asked_provider)s)
                    RETURNING id;
                """, client_data)
                client_id = cur.fetchone()[0]
                conn.commit()
                return client_id
        except Exception as e:
            print(f"Error creating client: {str(e)}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()

    def update_client(self, client_id: int, update_data: Dict) -> bool:
        """Update an existing client record."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                # Build the update query dynamically based on provided fields
                update_fields = []
                values = []
                for key, value in update_data.items():
                    if value is not None:
                        update_fields.append(f"{key} = %s")
                        values.append(value)
                
                if not update_fields:
                    return False
                
                # Add updated_at timestamp
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                
                query = f"""
                    UPDATE clients
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                """
                values.append(client_id)
                
                cur.execute(query, values)
                conn.commit()
                return True
        except Exception as e:
            print(f"Error updating client: {str(e)}")
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def infer_contract_type(self, phone_number: str) -> str:
        """Infer contract type based on appointment frequency."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT COUNT(*) FROM appointments
                    WHERE phone_number = %s
                    AND appointment_date >= CURRENT_DATE - INTERVAL '30 days'
                """, (phone_number,))
                count = cur.fetchone()[0]
                return "recurring" if count > 1 else "one-time"
        except Exception as e:
            print(f"Error inferring contract type: {str(e)}")
            return "one-time"
        finally:
            if 'conn' in locals():
                conn.close()

    def update_service_type(self, client_id: int, new_service: str) -> bool:
        """Update service type by appending new services if not already present."""
        try:
            conn = psycopg2.connect(**self.db_settings)
            with conn.cursor() as cur:
                # Get current services
                cur.execute("""
                    SELECT service_type FROM clients WHERE id = %s
                """, (client_id,))
                current_services = cur.fetchone()[0] or ""
                
                # Split into list and add new service if not present
                services = [s.strip() for s in current_services.split(',') if s.strip()]
                if new_service not in services:
                    services.append(new_service)
                
                # Update with new list
                cur.execute("""
                    UPDATE clients
                    SET service_type = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (', '.join(services), client_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"Error updating service type: {str(e)}")
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def process_client_data(self, message_data: Dict) -> Tuple[bool, str]:
        """
        Process client data from a message and update/create client record.
        Returns (success, message)
        """
        try:
            phone_number = message_data.get('phone_number')
            if not phone_number:
                return False, "Phone number is required"
            
            # Get or create client
            client = self.get_client_by_phone(phone_number)
            
            if client:
                # Update existing client
                update_data = {
                    'location': message_data.get('location'),
                    'contract_type': message_data.get('contract_type') or self.infer_contract_type(phone_number),
                    'most_asked_provider': message_data.get('most_asked_provider')
                }
                
                if self.update_client(client['id'], update_data):
                    if message_data.get('service_type'):
                        self.update_service_type(client['id'], message_data['service_type'])
                    return True, "Client updated successfully"
                else:
                    return False, "Failed to update client"
            else:
                # Create new client
                client_data = {
                    'client_name': message_data.get('client_name'),
                    'phone_number': phone_number,
                    'location': message_data.get('location'),
                    'contract_type': message_data.get('contract_type', 'one-time'),
                    'service_type': message_data.get('service_type'),
                    'most_asked_provider': message_data.get('most_asked_provider')
                }
                
                if self.create_client(client_data):
                    return True, "New client created successfully"
                else:
                    return False, "Failed to create client"
        except Exception as e:
            return False, f"Error processing client data: {str(e)}" 