import logging
from typing import List, Dict
import psycopg2
from psycopg2.extras import RealDictCursor

logger = logging.getLogger(__name__)

class DatabaseMigration:
    def __init__(self, db_name: str, db_user: str, db_password: str, db_host: str, db_port: str):
        """Initialize database connection for migrations."""
        self.db_name = db_name
        self.db_user = db_user
        self.db_password = db_password
        self.db_host = db_host
        self.db_port = db_port
        self.conn = None

    def connect(self):
        """Connect to the database."""
        try:
            self.conn = psycopg2.connect(
                dbname=self.db_name,
                user=self.db_user,
                password=self.db_password,
                host=self.db_host,
                port=self.db_port
            )
            logger.info("Successfully connected to database")
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise

    def disconnect(self):
        """Disconnect from the database."""
        if self.conn:
            self.conn.close()
            logger.info("Disconnected from database")

    def execute_migration(self, migration_sql: str) -> bool:
        """Execute a migration SQL statement."""
        try:
            with self.conn.cursor() as cur:
                cur.execute(migration_sql)
            self.conn.commit()
            logger.info("Migration executed successfully")
            return True
        except Exception as e:
            self.conn.rollback()
            logger.error(f"Error executing migration: {str(e)}")
            return False

    def verify_column_exists(self, table: str, column: str) -> bool:
        """Verify if a column exists in a table."""
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND column_name = %s
                """, (table, column))
                result = cur.fetchone()
                return bool(result)
        except Exception as e:
            logger.error(f"Error verifying column: {str(e)}")
            return False

    def get_table_structure(self, table: str) -> List[Dict]:
        """Get the structure of a table."""
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = %s
                    ORDER BY ordinal_position
                """, (table,))
                return cur.fetchall()
        except Exception as e:
            logger.error(f"Error getting table structure: {str(e)}")
            return []

def run_migrations():
    """Run all database migrations."""
    from app.utils.database import get_database_connection_params

    # Get database connection details using centralized utility
    params = get_database_connection_params()

    migration = DatabaseMigration(
        params['dbname'],
        params['user'],
        params['password'],
        params['host'],
        params['port']
    )

    try:
        migration.connect()

        # Migration 1: Add duration and recurring fields to appointments
        migration_sql = """
        ALTER TABLE appointments
        ADD COLUMN IF NOT EXISTS duration INTEGER,
        ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS recurrence_pattern JSONB,
        ADD COLUMN IF NOT EXISTS recurrence_end_date DATE;
        """

        if migration.execute_migration(migration_sql):
            # Verify the changes
            for column in ['duration', 'is_recurring', 'recurrence_pattern', 'recurrence_end_date']:
                if migration.verify_column_exists('appointments', column):
                    logger.info(f"Successfully added column: {column}")
                else:
                    logger.error(f"Failed to add column: {column}")

            # Print table structure for verification
            structure = migration.get_table_structure('appointments')
            logger.info("Current appointments table structure:")
            for col in structure:
                logger.info(f"Column: {col['column_name']}, Type: {col['data_type']}, Nullable: {col['is_nullable']}")

    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")
    finally:
        migration.disconnect()

if __name__ == "__main__":
    run_migrations()