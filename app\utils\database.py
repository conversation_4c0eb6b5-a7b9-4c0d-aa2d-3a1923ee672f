from datetime import datetime
from typing import List, Optional, Dict, Any
import psycopg2
from psycopg2.extras import RealDictCursor, DictCursor
from pydantic import BaseModel, Field
import os
from dotenv import load_dotenv
import logging
import threading
import atexit
from contextlib import contextmanager
from urllib.parse import urlparse
from psycopg2 import pool
from app.utils.exceptions import (
    DatabaseException, ErrorContext, handle_database_error, log_exception
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def get_database_connection_params() -> Dict[str, str]:
    """
    Get database connection parameters from environment variables.
    Supports both DATABASE_URL and individual parameters.
    Returns a dictionary with connection parameters.
    """
    load_dotenv()

    # Check if DATABASE_URL is provided
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        # Parse DATABASE_URL (format: postgresql://user:password@host:port/dbname)
        try:
            parsed = urlparse(database_url)

            # Validate that all required components are present
            if not parsed.username:
                raise ValueError("DATABASE_URL missing username")
            if not parsed.password:
                raise ValueError("DATABASE_URL missing password")
            if not parsed.hostname:
                raise ValueError("DATABASE_URL missing hostname")
            if not parsed.path or len(parsed.path) <= 1:
                raise ValueError("DATABASE_URL missing database name")

            return {
                'dbname': parsed.path[1:],  # Remove leading slash
                'user': parsed.username,
                'password': parsed.password,
                'host': parsed.hostname,
                'port': str(parsed.port or 5432)
            }
        except Exception as e:
            raise ValueError(f"Invalid DATABASE_URL format: {e}")

    # Fall back to individual parameters with validation
    required_vars = {
        'DB_USER': os.getenv('DB_USER'),
        'DB_PASSWORD': os.getenv('DB_PASSWORD')
    }

    # Check for missing required variables
    missing_vars = [var for var, value in required_vars.items() if not value]
    if missing_vars:
        raise ValueError(f"Missing required database environment variables: {', '.join(missing_vars)}")

    return {
        'dbname': os.getenv('DB_NAME', 'beauty_center'),
        'user': required_vars['DB_USER'],
        'password': required_vars['DB_PASSWORD'],
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '5432')
    }

def get_connection_pool_config() -> Dict[str, int]:
    """
    Get optimized connection pool configuration for beauty center scale.

    Returns:
        Dictionary with min_connections and max_connections
    """
    # Optimized defaults for beauty center workload (5-50 concurrent users)
    min_conn = int(os.getenv('DB_POOL_MIN_CONN', '3'))  # Slightly higher minimum
    max_conn = int(os.getenv('DB_POOL_MAX_CONN', '15'))  # Lower maximum for efficiency

    # Validate configuration
    if min_conn < 1:
        logger.warning("DB_POOL_MIN_CONN must be at least 1, using default value 3")
        min_conn = 3

    if max_conn < min_conn:
        logger.warning(f"DB_POOL_MAX_CONN ({max_conn}) must be >= DB_POOL_MIN_CONN ({min_conn}), adjusting to {min_conn + 5}")
        max_conn = min_conn + 5

    if max_conn > 50:
        logger.warning("DB_POOL_MAX_CONN optimized for beauty center scale, using 50 max")
        max_conn = 50

    return {
        'min_connections': min_conn,
        'max_connections': max_conn
    }


def create_database_connection(dbname: str = None) -> psycopg2.extensions.connection:
    """
    Create a database connection using standardized parameters.

    Args:
        dbname: Override database name (useful for connecting to 'postgres' for admin tasks)

    Returns:
        psycopg2 connection object
    """
    params = get_database_connection_params()
    if dbname:
        params['dbname'] = dbname

    try:
        return psycopg2.connect(**params)
    except Exception as e:
        logger.error(f"Error creating database connection: {e}")
        raise

class Worker(BaseModel):
    """Model for beauty center workers."""
    id: Optional[int] = None
    name: str
    specialization: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)

class WorkerPreference(BaseModel):
    """Model for worker preferences."""
    id: Optional[int] = None
    client_id: int
    worker_id: int
    preference_type: str = Field(..., pattern='^(preferred|blocked)$')
    created_at: datetime = Field(default_factory=datetime.now)

class Appointment(BaseModel):
    """Model for appointments with new fields."""
    id: Optional[int] = None
    client_name: str
    phone_number: str
    service_type: str
    appointment_date: datetime
    appointment_time: str
    location: str
    serial_number: str
    created_at: datetime = Field(default_factory=datetime.now)
    status: str = "pending"
    # New fields
    duration: Optional[int] = None  # in minutes
    is_recurring: bool = False
    recurrence_pattern: Optional[Dict] = None
    recurrence_end_date: Optional[datetime] = None
    worker_id: Optional[int] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DatabaseManager:
    """
    Singleton database manager with connection pooling for improved resource management.

    Features:
    - Thread-safe connection pooling
    - Automatic connection health checking
    - Resource leak prevention
    - Configurable pool size via environment variables
    """

    _instance = None
    _lock = threading.Lock()
    _connection_pool = None
    _pool_stats = {'created': 0, 'reused': 0, 'errors': 0}

    def __new__(cls):
        """Singleton pattern to ensure single connection pool across application."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize database connection pool with standardized parameters."""
        # Prevent re-initialization of singleton
        if hasattr(self, '_initialized'):
            return

        try:
            self.connection_params = get_database_connection_params()
            self.pool_config = get_connection_pool_config()
            self._init_connection_pool()
            self._init_db()
            self._initialized = True

            # Register cleanup on application exit
            atexit.register(self._cleanup_pool)

            logger.info("DatabaseManager initialized successfully with connection pooling")

        except Exception as e:
            logger.error(f"Failed to initialize DatabaseManager: {e}")
            raise

    def _init_connection_pool(self):
        """Initialize connection pool with configurable parameters."""
        try:
            self._connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=self.pool_config['min_connections'],
                maxconn=self.pool_config['max_connections'],
                **self.connection_params
            )

            logger.info(
                f"Database connection pool initialized: "
                f"{self.pool_config['min_connections']}-{self.pool_config['max_connections']} connections"
            )

            # Test the pool by getting and returning a connection
            test_conn = self._connection_pool.getconn()
            self._connection_pool.putconn(test_conn)
            logger.info("Connection pool test successful")

        except Exception as e:
            error_context = ErrorContext(
                operation="init_connection_pool",
                additional_data={
                    "min_connections": self.pool_config['min_connections'],
                    "max_connections": self.pool_config['max_connections']
                }
            )
            db_exception = handle_database_error(
                operation="initialize_connection_pool",
                original_exception=e,
                context=error_context
            )
            raise db_exception

    def _init_db(self):
        """Initialize database and create required tables using pooled connection."""
        try:
            # Connect to PostgreSQL server for admin tasks
            admin_conn = create_database_connection('postgres')
            admin_conn.autocommit = True

            with admin_conn.cursor() as cur:
                # Check if database exists
                cur.execute("SELECT 1 FROM pg_database WHERE datname = %s", (self.connection_params['dbname'],))
                if not cur.fetchone():
                    cur.execute(f"CREATE DATABASE {self.connection_params['dbname']}")
                    logger.info(f"Created database: {self.connection_params['dbname']}")

            admin_conn.close()

            # Create tables using pooled connection - but handle gracefully if they exist
            try:
                with self.get_connection() as conn:
                    self._create_tables_with_connection(conn)
            except Exception as table_error:
                # If tables already exist and we don't have permissions, that's OK
                error_msg = str(table_error).lower()
                if 'must be owner' in error_msg or 'permission denied' in error_msg:
                    logger.info("Tables already exist and user doesn't have modification permissions - continuing")
                else:
                    # Re-raise if it's a different error
                    raise table_error

        except Exception as e:
            error_context = ErrorContext(
                operation="init_database",
                additional_data={"database_name": self.connection_params['dbname']}
            )
            db_exception = handle_database_error(
                operation="database_initialization",
                original_exception=e,
                context=error_context
            )
            raise db_exception

    def _create_tables_with_connection(self, conn):
        """Create required tables using provided connection."""
        with conn.cursor() as cur:
            # Create workers table first
            cur.execute("""
                CREATE TABLE IF NOT EXISTS workers (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    specialization VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create clients table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS clients (
                    id SERIAL PRIMARY KEY,
                    client_name VARCHAR(255) NOT NULL,
                    phone_number VARCHAR(50) UNIQUE NOT NULL,
                    location VARCHAR(255),
                    contract_type VARCHAR(50) DEFAULT 'one-time',
                    service_type TEXT,
                    most_asked_provider VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create worker preferences table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS worker_preferences (
                    id SERIAL PRIMARY KEY,
                    client_id INTEGER REFERENCES clients(id),
                    worker_id INTEGER REFERENCES workers(id),
                    preference_type VARCHAR(20) CHECK (preference_type IN ('preferred', 'blocked')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create appointments table with new fields
            cur.execute("""
                CREATE TABLE IF NOT EXISTS appointments (
                    id SERIAL PRIMARY KEY,
                    client_name VARCHAR(255) NOT NULL,
                    phone_number VARCHAR(50) NOT NULL,
                    service_type VARCHAR(100) NOT NULL,
                    appointment_date DATE NOT NULL,
                    appointment_time TIME NOT NULL,
                    location VARCHAR(255) NOT NULL,
                    serial_number VARCHAR(50) UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE,
                    processed_at TIMESTAMP,
                    odoo_lead_id INTEGER
                )
            """)

            # Add new columns to appointments table if they don't exist
            cur.execute("""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                 WHERE table_name = 'appointments' AND column_name = 'duration') THEN
                        ALTER TABLE appointments ADD COLUMN duration INTEGER;
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                 WHERE table_name = 'appointments' AND column_name = 'is_recurring') THEN
                        ALTER TABLE appointments ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                 WHERE table_name = 'appointments' AND column_name = 'recurrence_pattern') THEN
                        ALTER TABLE appointments ADD COLUMN recurrence_pattern JSONB;
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                 WHERE table_name = 'appointments' AND column_name = 'recurrence_end_date') THEN
                        ALTER TABLE appointments ADD COLUMN recurrence_end_date DATE;
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                 WHERE table_name = 'appointments' AND column_name = 'worker_id') THEN
                        ALTER TABLE appointments ADD COLUMN worker_id INTEGER REFERENCES workers(id);
                    END IF;
                END $$;
            """)

            # Create indexes after all tables and columns exist
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_appointments_phone
                ON appointments(phone_number)
            """)

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_clients_phone
                ON clients(phone_number)
            """)

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_appointments_worker
                ON appointments(worker_id)
            """)

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_worker_preferences_client
                ON worker_preferences(client_id)
            """)

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_worker_preferences_worker
                ON worker_preferences(worker_id)
            """)

        conn.commit()
        logger.info("Database tables and indexes created/verified successfully")

    @contextmanager
    def get_connection(self):
        """
        Context manager for database connections from pool.

        Provides automatic connection management with:
        - Connection health checking
        - Proper cleanup on exceptions
        - Connection pool statistics tracking
        - Integration with standardized error handling

        Yields:
            psycopg2.connection: Database connection from pool
        """
        conn = None
        start_time = datetime.utcnow()

        try:
            # Get connection from pool
            conn = self._connection_pool.getconn()

            # Check connection health
            if conn.closed:
                logger.warning("Retrieved closed connection from pool, getting new one")
                self._connection_pool.putconn(conn, close=True)
                conn = self._connection_pool.getconn()
                self._pool_stats['created'] += 1
            else:
                self._pool_stats['reused'] += 1

            # Test connection with a simple query
            with conn.cursor() as test_cursor:
                test_cursor.execute("SELECT 1")
                test_cursor.fetchone()

            yield conn

        except Exception as e:
            self._pool_stats['errors'] += 1

            # Rollback any pending transaction
            if conn and not conn.closed:
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.warning(f"Error during rollback: {rollback_error}")

            # Create enhanced error context
            error_context = ErrorContext(
                operation="get_connection_from_pool",
                additional_data={
                    "connection_closed": conn.closed if conn else True,
                    "pool_stats": self._pool_stats.copy(),
                    "operation_duration_ms": (datetime.utcnow() - start_time).total_seconds() * 1000
                }
            )

            db_exception = handle_database_error(
                operation="connection_pool_management",
                original_exception=e,
                context=error_context
            )
            raise db_exception

        finally:
            # Always return connection to pool
            if conn:
                try:
                    self._connection_pool.putconn(conn)
                except Exception as putconn_error:
                    logger.error(f"Error returning connection to pool: {putconn_error}")
                    # Don't raise here to avoid masking original exception

    @contextmanager
    def get_cursor(self, cursor_factory=DictCursor):
        """
        Context manager for database cursor using pooled connections.

        Enhanced version that uses connection pool instead of persistent connection.
        Maintains backward compatibility with existing code.

        Args:
            cursor_factory: The cursor factory to use (default: DictCursor)
        """
        with self.get_connection() as conn:
            cursor = None
            try:
                cursor = conn.cursor(cursor_factory=cursor_factory)
                yield cursor
                conn.commit()

            except Exception as e:
                conn.rollback()
                # Use standardized database error handling from Task 2.1
                db_exception = handle_database_error(
                    operation="database_transaction",
                    original_exception=e,
                    context=ErrorContext(operation="get_cursor")
                )
                raise db_exception

            finally:
                if cursor:
                    cursor.close()

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a query and return results as dictionaries."""
        with self.get_cursor() as cur:
            cur.execute(query, params)
            return [dict(row) for row in cur.fetchall()]

    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an update query and return number of affected rows."""
        with self.get_cursor() as cur:
            cur.execute(query, params)
            return cur.rowcount

    def execute_single_query(self, query: str, params: tuple = None) -> Optional[Dict]:
        """
        Execute a query expecting a single result.

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            Single result as dictionary or None if no results
        """
        results = self.execute_query(query, params)
        return results[0] if results else None

    def get_by_field(self, table: str, field: str, value: Any) -> Optional[Dict]:
        """
        Generic get by field operation for any table.

        Args:
            table: Table name
            field: Field name to search by
            value: Value to search for

        Returns:
            Single record as dictionary or None if not found
        """
        query = f"SELECT * FROM {table} WHERE {field} = %s"
        return self.execute_single_query(query, (value,))

    def get_appointments_by_datetime(self, date_obj: Any, time_str: str) -> List[Dict]:
        """
        Get appointments by date and time - consolidates duplicated logic from tools.py.
        Optimized with LIMIT for early termination.

        Args:
            date_obj: Date object or string
            time_str: Time string in HH:MM format

        Returns:
            List of appointment dictionaries
        """
        query = """
            SELECT id FROM appointments
            WHERE appointment_date = %s
            AND appointment_time = %s
            AND status != 'cancelled'
            LIMIT 1
        """
        return self.execute_query(query, (date_obj, time_str))

    def get_all_appointments_ordered(self) -> List[Dict]:
        """
        Get all appointments ordered by date/time - consolidates duplicated logic from tools.py.

        Returns:
            List of all appointments ordered by date and time
        """
        query = """
            SELECT * FROM appointments
            ORDER BY appointment_date DESC, appointment_time DESC
        """
        return self.execute_query(query)

    def get_client(self, phone_number: str) -> Optional[Dict]:
        """
        Get client by phone number with simple in-memory caching.

        Handles permission errors gracefully by returning None if client table
        is not accessible.
        """
        # Initialize client cache if not exists
        if not hasattr(self, '_client_cache'):
            self._client_cache = {}

        # Check cache first
        if phone_number in self._client_cache:
            return self._client_cache[phone_number]

        try:
            # Query database if not cached
            result = self.execute_query(
                "SELECT * FROM clients WHERE phone_number = %s",
                (phone_number,)
            )
            client = result[0] if result else None

            # Cache the result (including None for non-existent clients)
            self._client_cache[phone_number] = client

            # Limit cache size to prevent memory issues
            if len(self._client_cache) > 1000:
                # Remove oldest 20% of entries
                items = list(self._client_cache.items())
                for phone, _ in items[:200]:
                    del self._client_cache[phone]

            return client

        except Exception as e:
            # Check if it's a permission error
            error_msg = str(e).lower()
            if 'permission denied' in error_msg and 'clients' in error_msg:
                logger.warning(f"Client table permission denied for phone {phone_number[:10]}... - returning None")
                # Cache None to avoid repeated failed queries
                self._client_cache[phone_number] = None
                return None
            else:
                # Re-raise other errors
                raise e

    def save_client(self, client_data: Dict) -> int:
        """
        Save or update client data with graceful permission handling.

        If client table operations fail due to permissions, log the error
        but don't break the booking flow.
        """
        try:
            existing = self.get_client(client_data['phone_number'])

            if existing:
                # Update existing client
                query = """
                    UPDATE clients
                    SET client_name = %s,
                        location = %s,
                        contract_type = %s,
                        service_type = %s,
                        most_asked_provider = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE phone_number = %s
                    RETURNING id
                """
                params = (
                    client_data['client_name'],
                    client_data.get('location'),
                    client_data.get('contract_type', 'one-time'),
                    client_data.get('service_type'),
                    client_data.get('most_asked_provider'),
                    client_data['phone_number']
                )
            else:
                # Create new client
                query = """
                    INSERT INTO clients
                    (client_name, phone_number, location, contract_type,
                     service_type, most_asked_provider)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING id
                """
                params = (
                    client_data['client_name'],
                    client_data['phone_number'],
                    client_data.get('location'),
                    client_data.get('contract_type', 'one-time'),
                    client_data.get('service_type'),
                    client_data.get('most_asked_provider')
                )

            result = self.execute_query(query, params)
            return result[0]['id'] if result else None

        except Exception as e:
            # Check if it's a permission error
            error_msg = str(e).lower()
            if 'permission denied' in error_msg and 'clients' in error_msg:
                logger.warning(f"Client table permission denied - continuing without client record: {e}")
                # Return a dummy ID to indicate client handling was attempted
                return -1  # Special value indicating permission issue
            else:
                # Re-raise other errors
                raise e

    def save_appointment(self, appointment_data: Dict) -> int:
        """Save appointment data."""
        query = """
            INSERT INTO appointments
            (client_name, phone_number, service_type,
             appointment_date, appointment_time, location, serial_number)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        params = (
            appointment_data['client_name'],
            appointment_data['phone_number'],
            appointment_data['service_type'],
            appointment_data['appointment_date'],
            appointment_data['appointment_time'],
            appointment_data.get('location', 'Not specified'),  # Use get() with default value
            "" # Temporary value for serial_number
        )

        result = self.execute_query(query, params)
        appointment_id = result[0]['id'] if result else None
        if appointment_id:
            serial_number = f"APPT-{appointment_id:06d}"
            with self.get_cursor() as cur:
                cur.execute("""
                    UPDATE appointments
                    SET serial_number = %s
                    WHERE id = %s
                """, (serial_number, appointment_id))
        return appointment_id


    def get_unprocessed_appointments(self) -> List[Dict]:
        """Get unprocessed appointments with client information."""
        query = """
            SELECT a.*, c.location, c.contract_type, c.most_asked_provider
            FROM appointments a
            LEFT JOIN clients c ON a.phone_number = c.phone_number
            WHERE a.processed = FALSE
            ORDER BY a.created_at ASC
        """
        return self.execute_query(query)

    def mark_appointment_processed(self, appointment_id: int, odoo_lead_id: int) -> bool:
        """Mark appointment as processed."""
        query = """
            UPDATE appointments
            SET processed = TRUE,
                processed_at = CURRENT_TIMESTAMP,
                odoo_lead_id = %s
            WHERE id = %s
        """
        return self.execute_update(query, (odoo_lead_id, appointment_id)) > 0

    def get_pool_stats(self) -> Dict[str, Any]:
        """
        Get connection pool statistics for monitoring.

        Returns:
            Dictionary with pool statistics and health information
        """
        try:
            # Get current pool status
            pool_info = {
                'min_connections': self.pool_config['min_connections'],
                'max_connections': self.pool_config['max_connections'],
                'stats': self._pool_stats.copy(),
                'pool_closed': self._connection_pool.closed if self._connection_pool else True
            }

            return pool_info

        except Exception as e:
            logger.warning(f"Error getting pool stats: {e}")
            return {
                'error': str(e),
                'stats': self._pool_stats.copy()
            }

    def _cleanup_pool(self):
        """Clean up connection pool on application shutdown."""
        try:
            if self._connection_pool and not self._connection_pool.closed:
                self._connection_pool.closeall()
                logger.info("Database connection pool closed successfully")

                # Log final statistics
                logger.info(f"Final pool statistics: {self._pool_stats}")

        except Exception as e:
            logger.error(f"Error during connection pool cleanup: {e}")

    def __del__(self):
        """Close database connection and cleanup pool."""
        # For backward compatibility, keep the old behavior
        if hasattr(self, 'conn') and hasattr(self.conn, 'close'):
            try:
                self.conn.close()
            except Exception:
                pass  # Ignore errors during cleanup

        # Also cleanup the pool
        self._cleanup_pool()

    def create_appointment(self, appointment: Appointment) -> Appointment:
        try:
            with self.get_cursor() as cur:
                cur.execute("""
                    INSERT INTO appointments
                    (client_name, phone_number, service_type, appointment_date, appointment_time, location, serial_number, processed)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE)
                    RETURNING id
                """, (
                    appointment.client_name,
                    appointment.phone_number,
                    appointment.service_type,
                    appointment.appointment_date,
                    appointment.appointment_time,
                    appointment.location,
                    "" # Temporary value for serial_number
                ))
                appointment_id = cur.fetchone()[0]

            # Generate a unique serial number based on the appointment_id.
            serial_number = f"APPT-{appointment_id:06d}"

            # Update the appointment with the generated serial_number.
            with self.get_cursor() as cur_update:
                cur_update.execute("""
                    UPDATE appointments
                    SET serial_number = %s
                    WHERE id = %s
                """, (serial_number, appointment_id))

            # Set the id and serial_number on the Pydantic model.
            appointment.id = appointment_id
            appointment.serial_number = serial_number
            return appointment

        except Exception as e:
            # Use standardized database error handling
            db_exception = handle_database_error(
                operation="create_appointment",
                table="appointments",
                original_exception=e,
                context=ErrorContext(
                    operation="create_appointment",
                    additional_data={"client_name": appointment.client_name}
                )
            )
            raise db_exception

    def get_appointments_by_phone(self, phone_number: str) -> List[Appointment]:
        try:
            with self.get_cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT * FROM appointments
                    WHERE phone_number = %s
                    ORDER BY appointment_date, appointment_time
                """, (phone_number,))
                rows = cur.fetchall()
                return [Appointment(**row) for row in rows]
        except Exception as e:
            # Use standardized database error handling
            db_exception = handle_database_error(
                operation="get_appointments_by_phone",
                table="appointments",
                original_exception=e,
                context=ErrorContext(
                    operation="get_appointments_by_phone",
                    user_id=phone_number
                )
            )
            raise db_exception

    def get_appointment_by_id(self, appointment_id: int) -> Optional[Appointment]:
        """Get appointment by ID."""
        try:
            with self.get_cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT
                        id,
                        client_name,
                        phone_number,
                        service_type,
                        appointment_date,
                        appointment_time::text as appointment_time,
                        location,
                        serial_number,
                        created_at,
                        status
                    FROM appointments
                    WHERE id = %s
                """, (appointment_id,))
                row = cur.fetchone()
                if row:
                    return Appointment(**row)
                return None
        except Exception as e:
            # Use standardized database error handling
            db_exception = handle_database_error(
                operation="get_appointment_by_id",
                table="appointments",
                original_exception=e,
                context=ErrorContext(
                    operation="get_appointment_by_id",
                    additional_data={"appointment_id": appointment_id}
                )
            )
            raise db_exception

    def get_available_time_slots(self, date: datetime) -> List[str]:
        """Get available time slots for a given date."""
        try:
            with self.get_cursor() as cur:
                cur.execute("""
                    SELECT appointment_time::text
                    FROM appointments
                    WHERE appointment_date = %s
                    AND status != 'cancelled'
                """, (date,))
                booked_times = [row[0][:5] for row in cur.fetchall()]  # Convert HH:MM:SS to HH:MM

            # Define all possible time slots (adjust based on business hours)
            all_slots = [
                "09:00", "10:00", "11:00", "12:00", "13:00",
                "14:00", "15:00", "16:00", "17:00"
            ]
            available_slots = [slot for slot in all_slots if slot not in booked_times]

            logger.debug(f"Booked times for {date.date()}: {booked_times}")
            logger.debug(f"Available slots for {date.date()}: {available_slots}")

            return available_slots
        except Exception as e:
            # Use standardized database error handling
            db_exception = handle_database_error(
                operation="get_available_time_slots",
                table="appointments",
                original_exception=e,
                context=ErrorContext(
                    operation="get_available_time_slots",
                    additional_data={"date": str(date.date())}
                )
            )
            raise db_exception