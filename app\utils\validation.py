"""
Centralized input validation utilities for Task 1.4
Provides secure validation and sanitization for all user inputs
"""

import re
import html
import logging
from datetime import datetime, date
from typing import Optional, Dict, List, Any, Union
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)

# Security constants
MAX_INPUT_LENGTH = 1000
MAX_MESSAGE_LENGTH = 4000
MAX_NAME_LENGTH = 100
MAX_LOCATION_LENGTH = 200
MAX_PHONE_LENGTH = 30  # Increased to accommodate WhatsApp format: whatsapp:+1234567890123456

# Validation patterns
PHONE_PATTERN = re.compile(r'^whatsapp:\+[1-9]\d{1,14}$')  # WhatsApp format with required + sign
DATE_PATTERN = re.compile(r'^\d{4}-\d{2}-\d{2}$')
TIME_PATTERN = re.compile(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$')
NAME_PATTERN = re.compile(r'^[a-zA-Z\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\u0590-\u05FF.-]{1,100}$')

# Allowed services (from tools.py)
ALLOWED_SERVICES = ["cleaning", "facial", "massage", "waxing"]

class ValidationError(Exception):
    """Custom exception for validation errors."""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)

class WebhookInput(BaseModel):
    """Validated webhook input model."""
    Body: str = Field(..., min_length=1, max_length=MAX_MESSAGE_LENGTH)
    From: str = Field(..., min_length=1, max_length=MAX_PHONE_LENGTH)

    @validator('Body')
    def validate_body(cls, v):
        if not v or not v.strip():
            raise ValueError("Message body cannot be empty")
        return sanitize_text_input(v.strip())

    @validator('From')
    def validate_from(cls, v):
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty")
        sanitized = v.strip()
        if not is_valid_phone_number(sanitized):
            raise ValueError("Invalid phone number format")
        return sanitized

class AppointmentInput(BaseModel):
    """Validated appointment input model."""
    client_name: str = Field(..., min_length=1, max_length=MAX_NAME_LENGTH)
    phone_number: str = Field(..., min_length=1, max_length=MAX_PHONE_LENGTH)
    service_type: str = Field(..., min_length=1, max_length=50)
    appointment_date: str = Field(..., min_length=10, max_length=10)
    appointment_time: str = Field(..., min_length=5, max_length=5)
    location: Optional[str] = Field(None, max_length=MAX_LOCATION_LENGTH)
    duration: Optional[int] = Field(None, ge=15, le=480)  # 15 minutes to 8 hours

    @validator('client_name')
    def validate_client_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Client name cannot be empty")
        sanitized = sanitize_text_input(v.strip())
        if not is_valid_name(sanitized):
            raise ValueError("Client name contains invalid characters")
        return sanitized

    @validator('phone_number')
    def validate_phone_number(cls, v):
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty")
        sanitized = v.strip()
        if not is_valid_phone_number(sanitized):
            raise ValueError("Invalid phone number format")
        return sanitized

    @validator('service_type')
    def validate_service_type(cls, v):
        if not v or not v.strip():
            raise ValueError("Service type cannot be empty")
        sanitized = v.strip().lower()
        if sanitized not in ALLOWED_SERVICES:
            raise ValueError(f"Invalid service type. Must be one of: {', '.join(ALLOWED_SERVICES)}")
        return sanitized

    @validator('appointment_date')
    def validate_appointment_date(cls, v):
        if not v or not v.strip():
            raise ValueError("Appointment date cannot be empty")
        sanitized = v.strip()
        if not is_valid_date(sanitized):
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
        # Check if date is not in the past
        try:
            date_obj = datetime.strptime(sanitized, "%Y-%m-%d").date()
            if date_obj < date.today():
                raise ValueError("Appointment date cannot be in the past")
        except ValueError as e:
            if "past" in str(e):
                raise e
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
        return sanitized

    @validator('appointment_time')
    def validate_appointment_time(cls, v):
        if not v or not v.strip():
            raise ValueError("Appointment time cannot be empty")
        sanitized = v.strip()
        if not is_valid_time(sanitized):
            raise ValueError("Invalid time format. Use HH:MM")
        return sanitized

    @validator('location')
    def validate_location(cls, v):
        if v is None:
            return None
        sanitized = sanitize_text_input(v.strip()) if v.strip() else None
        return sanitized if sanitized else None

def sanitize_text_input(text: str) -> str:
    """
    Sanitize text input to prevent XSS and other injection attacks.

    Args:
        text: Raw text input

    Returns:
        Sanitized text safe for processing and storage
    """
    if not text:
        return ""

    # HTML escape to prevent XSS
    sanitized = html.escape(text.strip())

    # Remove null bytes and other control characters
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\n\r\t')

    # Limit length
    if len(sanitized) > MAX_INPUT_LENGTH:
        sanitized = sanitized[:MAX_INPUT_LENGTH]
        logger.warning(f"Input truncated to {MAX_INPUT_LENGTH} characters")

    return sanitized

def is_valid_phone_number(phone: str) -> bool:
    """
    Validate phone number format for WhatsApp.

    Args:
        phone: Phone number string

    Returns:
        True if valid, False otherwise
    """
    if not phone:
        return False

    # Check basic WhatsApp format
    if phone.startswith('whatsapp:'):
        return bool(PHONE_PATTERN.match(phone))

    # Check if it's a valid phone number that can be converted
    cleaned = ''.join(filter(str.isdigit, phone))
    return 10 <= len(cleaned) <= 15

def is_valid_date(date_str: str) -> bool:
    """
    Validate date string format.

    Args:
        date_str: Date string in YYYY-MM-DD format

    Returns:
        True if valid, False otherwise
    """
    if not date_str or not DATE_PATTERN.match(date_str):
        return False

    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False

def is_valid_time(time_str: str) -> bool:
    """
    Validate time string format.

    Args:
        time_str: Time string in HH:MM format

    Returns:
        True if valid, False otherwise
    """
    if not time_str or not TIME_PATTERN.match(time_str):
        return False

    try:
        datetime.strptime(time_str, "%H:%M")
        return True
    except ValueError:
        return False

def is_valid_name(name: str) -> bool:
    """
    Validate name contains only allowed characters.

    Args:
        name: Name string

    Returns:
        True if valid, False otherwise
    """
    if not name:
        return False

    return bool(NAME_PATTERN.match(name))

def validate_request_size(content_length: Optional[int]) -> bool:
    """
    Validate request size to prevent DoS attacks.

    Args:
        content_length: Content length from request headers

    Returns:
        True if valid size, False otherwise
    """
    if content_length is None:
        return True  # Allow requests without content-length header

    # Maximum request size: 1MB
    MAX_REQUEST_SIZE = 1024 * 1024
    return content_length <= MAX_REQUEST_SIZE

def sanitize_error_message(error: Exception) -> str:
    """
    Sanitize error messages to prevent information disclosure.

    Args:
        error: Exception object

    Returns:
        Safe error message for user display
    """
    error_str = str(error).lower()

    # Check for sensitive information patterns
    sensitive_patterns = [
        'password', 'token', 'key', 'secret', 'database', 'connection',
        'sql', 'query', 'table', 'column', 'schema', 'host', 'port',
        'user', 'admin', 'root', 'config', 'env', 'path', 'file'
    ]

    for pattern in sensitive_patterns:
        if pattern in error_str:
            logger.warning(f"Sensitive information detected in error: {error}")
            return "An error occurred while processing your request. Please try again."

    # Return sanitized version of the original error
    return sanitize_text_input(str(error))

def log_validation_failure(field: str, value: str, error: str, user_id: str = None):
    """
    Log validation failures for security monitoring.

    Args:
        field: Field name that failed validation
        value: The invalid value (truncated for security)
        error: Validation error message
        user_id: User identifier (phone number)
    """
    # Truncate value for logging (don't log full content)
    safe_value = value[:50] + "..." if len(value) > 50 else value
    safe_value = sanitize_text_input(safe_value)

    logger.warning(
        f"Validation failure - Field: {field}, Value: {safe_value}, "
        f"Error: {error}, User: {user_id or 'unknown'}"
    )

def create_validation_response(error: ValidationError, language: str = 'en') -> str:
    """
    Create user-friendly validation error response.

    Args:
        error: ValidationError object
        language: Response language

    Returns:
        User-friendly error message
    """
    # Map common validation errors to user-friendly messages
    error_messages = {
        'en': {
            'phone': "Please provide a valid phone number.",
            'date': "Please provide a valid date in YYYY-MM-DD format.",
            'time': "Please provide a valid time in HH:MM format.",
            'name': "Please provide a valid name using only letters and spaces.",
            'service': f"Please choose a valid service: {', '.join(ALLOWED_SERVICES)}.",
            'location': "Please provide a valid location.",
            'general': "Please check your input and try again."
        },
        'ar': {
            'phone': "يرجى تقديم رقم هاتف صحيح.",
            'date': "يرجى تقديم تاريخ صحيح بصيغة YYYY-MM-DD.",
            'time': "يرجى تقديم وقت صحيح بصيغة HH:MM.",
            'name': "يرجى تقديم اسم صحيح باستخدام الحروف والمسافات فقط.",
            'service': f"يرجى اختيار خدمة صحيحة: {', '.join(ALLOWED_SERVICES)}.",
            'location': "يرجى تقديم موقع صحيح.",
            'general': "يرجى التحقق من المدخلات والمحاولة مرة أخرى."
        }
    }

    messages = error_messages.get(language, error_messages['en'])

    # Determine error type based on field or message
    if error.field:
        if 'phone' in error.field.lower():
            return messages['phone']
        elif 'date' in error.field.lower():
            return messages['date']
        elif 'time' in error.field.lower():
            return messages['time']
        elif 'name' in error.field.lower():
            return messages['name']
        elif 'service' in error.field.lower():
            return messages['service']
        elif 'location' in error.field.lower():
            return messages['location']

    return messages['general']
