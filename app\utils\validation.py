"""
Centralized input validation utilities for Task 1.4
Provides secure validation and sanitization for all user inputs
"""

import re
import html
import logging
from datetime import datetime, date
from typing import Optional, Dict, List, Any, Union
from pydantic import BaseModel, Field, validator, model_validator

logger = logging.getLogger(__name__)

# Security constants
MAX_INPUT_LENGTH = 1000
MAX_MESSAGE_LENGTH = 4000
MAX_NAME_LENGTH = 100
MAX_LOCATION_LENGTH = 200
MAX_PHONE_LENGTH = 30  # Increased to accommodate WhatsApp format: whatsapp:+1234567890123456
MAX_MEDIA_URL_LENGTH = 500  # Maximum length for Twilio MediaUrl
MAX_CONTENT_TYPE_LENGTH = 100  # Maximum length for MediaContentType
MAX_AUDIO_FILE_SIZE_MB = 16  # Maximum audio file size for WhatsApp (16MB)

# Validation patterns
PHONE_PATTERN = re.compile(r'^whatsapp:\+[1-9]\d{1,14}$')  # WhatsApp format with required + sign
DATE_PATTERN = re.compile(r'^\d{4}-\d{2}-\d{2}$')
TIME_PATTERN = re.compile(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$')
NAME_PATTERN = re.compile(r'^[a-zA-Z\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\u0590-\u05FF.-]{1,100}$')

# Allowed services (from tools.py)
ALLOWED_SERVICES = ["cleaning", "facial", "massage", "waxing"]

# Supported audio formats for WhatsApp voice messages (based on Twilio documentation)
SUPPORTED_AUDIO_FORMATS = [
    "audio/ogg",
    "audio/mpeg",
    "audio/mp4",
    "audio/mp3",
    "audio/3gpp",
    "audio/amr",
    "audio/amr-nb",
    "audio/webm"
]

class ValidationError(Exception):
    """Custom exception for validation errors."""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(message)

class WebhookInput(BaseModel):
    """
    Validated webhook input model for Twilio WhatsApp messages.
    Supports both text messages and voice messages with media attachments.
    """
    # Required fields for all messages
    Body: str = Field(..., max_length=MAX_MESSAGE_LENGTH)  # Allow empty body for voice messages
    From: str = Field(..., min_length=1, max_length=MAX_PHONE_LENGTH)

    # Optional fields for voice messages and media
    NumMedia: Optional[int] = Field(default=0, ge=0, le=10, description="Number of media attachments")
    MediaUrl0: Optional[str] = Field(default=None, max_length=MAX_MEDIA_URL_LENGTH, description="URL to first media attachment")
    MediaContentType0: Optional[str] = Field(default=None, max_length=MAX_CONTENT_TYPE_LENGTH, description="Content type of first media attachment")

    @validator('Body')
    def validate_body(cls, v):
        """Basic validation and sanitization of message body."""
        # Always sanitize the input, but don't check for emptiness here
        # The emptiness check will be done in the root validator
        return sanitize_text_input(v) if v else ""

    @validator('From')
    def validate_from(cls, v):
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty")
        sanitized = v.strip()
        if not is_valid_phone_number(sanitized):
            raise ValueError("Invalid phone number format")
        return sanitized

    @validator('MediaUrl0')
    def validate_media_url(cls, v, values):
        """Validate media URL when present."""
        if v is not None:
            # Check if NumMedia indicates media is present
            num_media = values.get('NumMedia', 0)
            if num_media == 0:
                raise ValueError("MediaUrl0 provided but NumMedia is 0")

            # Basic URL validation
            if not v.strip():
                raise ValueError("MediaUrl0 cannot be empty when provided")

            # Ensure it's a valid Twilio media URL format
            if not v.startswith('https://api.twilio.com/'):
                raise ValueError("Invalid MediaUrl0 format - must be a Twilio API URL")

        return v

    @validator('MediaContentType0')
    def validate_media_content_type(cls, v, values):
        """Validate media content type for voice messages."""
        if v is not None:
            # Check if NumMedia indicates media is present
            num_media = values.get('NumMedia', 0)
            if num_media == 0:
                raise ValueError("MediaContentType0 provided but NumMedia is 0")

            # Validate content type format
            if not v.strip():
                raise ValueError("MediaContentType0 cannot be empty when provided")

            # For voice messages, validate audio format
            if v.startswith('audio/'):
                if v not in SUPPORTED_AUDIO_FORMATS:
                    supported_formats = ', '.join(SUPPORTED_AUDIO_FORMATS)
                    raise ValueError(f"Unsupported audio format '{v}'. Supported formats: {supported_formats}")

        return v

    def is_voice_message(self) -> bool:
        """
        Determine if this webhook input represents a voice message.

        Returns:
            True if this is a voice message, False otherwise
        """
        return (
            self.NumMedia > 0 and
            self.MediaContentType0 is not None and
            self.MediaContentType0.startswith('audio/')
        )

    @model_validator(mode='after')
    def validate_message_content(self):
        """Validate that either body has content or it's a valid voice message."""
        # Check if this is a voice message
        is_voice = (
            self.NumMedia > 0 and
            self.MediaContentType0 is not None and
            self.MediaContentType0.startswith('audio/')
        )

        # For text messages, body cannot be empty
        if not is_voice and (not self.Body or not self.Body.strip()):
            raise ValueError("Message body cannot be empty for text messages")

        return self

class AppointmentInput(BaseModel):
    """Validated appointment input model."""
    client_name: str = Field(..., min_length=1, max_length=MAX_NAME_LENGTH)
    phone_number: str = Field(..., min_length=1, max_length=MAX_PHONE_LENGTH)
    service_type: str = Field(..., min_length=1, max_length=50)
    appointment_date: str = Field(..., min_length=10, max_length=10)
    appointment_time: str = Field(..., min_length=5, max_length=5)
    location: Optional[str] = Field(None, max_length=MAX_LOCATION_LENGTH)
    duration: Optional[int] = Field(None, ge=15, le=480)  # 15 minutes to 8 hours

    @validator('client_name')
    def validate_client_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Client name cannot be empty")
        sanitized = sanitize_text_input(v.strip())
        if not is_valid_name(sanitized):
            raise ValueError("Client name contains invalid characters")
        return sanitized

    @validator('phone_number')
    def validate_phone_number(cls, v):
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty")
        sanitized = v.strip()
        if not is_valid_phone_number(sanitized):
            raise ValueError("Invalid phone number format")
        return sanitized

    @validator('service_type')
    def validate_service_type(cls, v):
        if not v or not v.strip():
            raise ValueError("Service type cannot be empty")
        sanitized = v.strip().lower()
        if sanitized not in ALLOWED_SERVICES:
            raise ValueError(f"Invalid service type. Must be one of: {', '.join(ALLOWED_SERVICES)}")
        return sanitized

    @validator('appointment_date')
    def validate_appointment_date(cls, v):
        if not v or not v.strip():
            raise ValueError("Appointment date cannot be empty")
        sanitized = v.strip()
        if not is_valid_date(sanitized):
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
        # Check if date is not in the past
        try:
            date_obj = datetime.strptime(sanitized, "%Y-%m-%d").date()
            if date_obj < date.today():
                raise ValueError("Appointment date cannot be in the past")
        except ValueError as e:
            if "past" in str(e):
                raise e
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
        return sanitized

    @validator('appointment_time')
    def validate_appointment_time(cls, v):
        if not v or not v.strip():
            raise ValueError("Appointment time cannot be empty")
        sanitized = v.strip()
        if not is_valid_time(sanitized):
            raise ValueError("Invalid time format. Use HH:MM")
        return sanitized

    @validator('location')
    def validate_location(cls, v):
        if v is None:
            return None
        sanitized = sanitize_text_input(v.strip()) if v.strip() else None
        return sanitized if sanitized else None

def sanitize_text_input(text: str) -> str:
    """
    Sanitize text input to prevent XSS and other injection attacks.

    Args:
        text: Raw text input

    Returns:
        Sanitized text safe for processing and storage
    """
    if not text:
        return ""

    # HTML escape to prevent XSS
    sanitized = html.escape(text.strip())

    # Remove null bytes and other control characters
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\n\r\t')

    # Limit length
    if len(sanitized) > MAX_INPUT_LENGTH:
        sanitized = sanitized[:MAX_INPUT_LENGTH]
        logger.warning(f"Input truncated to {MAX_INPUT_LENGTH} characters")

    return sanitized

def is_valid_phone_number(phone: str) -> bool:
    """
    Validate phone number format for WhatsApp.

    Args:
        phone: Phone number string

    Returns:
        True if valid, False otherwise
    """
    if not phone:
        return False

    # Check basic WhatsApp format
    if phone.startswith('whatsapp:'):
        return bool(PHONE_PATTERN.match(phone))

    # Check if it's a valid phone number that can be converted
    cleaned = ''.join(filter(str.isdigit, phone))
    return 10 <= len(cleaned) <= 15

def is_valid_date(date_str: str) -> bool:
    """
    Validate date string format.

    Args:
        date_str: Date string in YYYY-MM-DD format

    Returns:
        True if valid, False otherwise
    """
    if not date_str or not DATE_PATTERN.match(date_str):
        return False

    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False

def is_valid_time(time_str: str) -> bool:
    """
    Validate time string format.

    Args:
        time_str: Time string in HH:MM format

    Returns:
        True if valid, False otherwise
    """
    if not time_str or not TIME_PATTERN.match(time_str):
        return False

    try:
        datetime.strptime(time_str, "%H:%M")
        return True
    except ValueError:
        return False

def is_valid_name(name: str) -> bool:
    """
    Validate name contains only allowed characters.

    Args:
        name: Name string

    Returns:
        True if valid, False otherwise
    """
    if not name:
        return False

    return bool(NAME_PATTERN.match(name))

def is_valid_audio_format(content_type: str) -> bool:
    """
    Validate if the content type represents a supported audio format.

    Args:
        content_type: MIME type string (e.g., 'audio/ogg')

    Returns:
        True if supported audio format, False otherwise
    """
    if not content_type:
        return False

    return content_type.lower().strip() in SUPPORTED_AUDIO_FORMATS

def is_valid_media_url(url: str) -> bool:
    """
    Validate if the URL is a valid Twilio media URL.

    Args:
        url: Media URL string

    Returns:
        True if valid Twilio media URL, False otherwise
    """
    if not url:
        return False

    # Basic validation for Twilio media URLs
    url = url.strip()
    return (
        url.startswith('https://api.twilio.com/') and
        '/Media/' in url and
        len(url) <= MAX_MEDIA_URL_LENGTH
    )

def validate_voice_message_size(content_length: Optional[int]) -> bool:
    """
    Validate voice message file size is within WhatsApp limits.

    Args:
        content_length: Content length in bytes

    Returns:
        True if within size limits, False otherwise
    """
    if content_length is None:
        return True  # Cannot validate without size info

    # WhatsApp voice message limit: 16MB
    max_size_bytes = MAX_AUDIO_FILE_SIZE_MB * 1024 * 1024
    return content_length <= max_size_bytes

def validate_request_size(content_length: Optional[int]) -> bool:
    """
    Validate request size to prevent DoS attacks.

    Args:
        content_length: Content length from request headers

    Returns:
        True if valid size, False otherwise
    """
    if content_length is None:
        return True  # Allow requests without content-length header

    # Maximum request size: 1MB
    MAX_REQUEST_SIZE = 1024 * 1024
    return content_length <= MAX_REQUEST_SIZE

def sanitize_error_message(error: Exception) -> str:
    """
    Sanitize error messages to prevent information disclosure.

    Args:
        error: Exception object

    Returns:
        Safe error message for user display
    """
    error_str = str(error).lower()

    # Check for sensitive information patterns
    sensitive_patterns = [
        'password', 'token', 'key', 'secret', 'database', 'connection',
        'sql', 'query', 'table', 'column', 'schema', 'host', 'port',
        'user', 'admin', 'root', 'config', 'env', 'path', 'file'
    ]

    for pattern in sensitive_patterns:
        if pattern in error_str:
            logger.warning(f"Sensitive information detected in error: {error}")
            return "An error occurred while processing your request. Please try again."

    # Return sanitized version of the original error
    return sanitize_text_input(str(error))

def log_validation_failure(field: str, value: str, error: str, user_id: str = None):
    """
    Log validation failures for security monitoring.

    Args:
        field: Field name that failed validation
        value: The invalid value (truncated for security)
        error: Validation error message
        user_id: User identifier (phone number)
    """
    # Truncate value for logging (don't log full content)
    safe_value = value[:50] + "..." if len(value) > 50 else value
    safe_value = sanitize_text_input(safe_value)

    logger.warning(
        f"Validation failure - Field: {field}, Value: {safe_value}, "
        f"Error: {error}, User: {user_id or 'unknown'}"
    )

def create_validation_response(error: ValidationError, language: str = 'en') -> str:
    """
    Create user-friendly validation error response.

    Args:
        error: ValidationError object
        language: Response language

    Returns:
        User-friendly error message
    """
    # Map common validation errors to user-friendly messages
    error_messages = {
        'en': {
            'phone': "Please provide a valid phone number.",
            'date': "Please provide a valid date in YYYY-MM-DD format.",
            'time': "Please provide a valid time in HH:MM format.",
            'name': "Please provide a valid name using only letters and spaces.",
            'service': f"Please choose a valid service: {', '.join(ALLOWED_SERVICES)}.",
            'location': "Please provide a valid location.",
            'audio_format': f"Unsupported audio format. Please send voice messages in one of these formats: {', '.join(SUPPORTED_AUDIO_FORMATS)}.",
            'media_url': "Invalid media attachment. Please try sending your voice message again.",
            'media_size': f"Voice message is too large. Maximum size allowed is {MAX_AUDIO_FILE_SIZE_MB}MB.",
            'voice_processing': "I'm having trouble processing your voice message. Please try sending it as text instead.",
            'general': "Please check your input and try again."
        },
        'ar': {
            'phone': "يرجى تقديم رقم هاتف صحيح.",
            'date': "يرجى تقديم تاريخ صحيح بصيغة YYYY-MM-DD.",
            'time': "يرجى تقديم وقت صحيح بصيغة HH:MM.",
            'name': "يرجى تقديم اسم صحيح باستخدام الحروف والمسافات فقط.",
            'service': f"يرجى اختيار خدمة صحيحة: {', '.join(ALLOWED_SERVICES)}.",
            'location': "يرجى تقديم موقع صحيح.",
            'audio_format': f"تنسيق الصوت غير مدعوم. يرجى إرسال الرسائل الصوتية بأحد هذه التنسيقات: {', '.join(SUPPORTED_AUDIO_FORMATS)}.",
            'media_url': "مرفق الوسائط غير صالح. يرجى المحاولة مرة أخرى لإرسال رسالتك الصوتية.",
            'media_size': f"الرسالة الصوتية كبيرة جداً. الحد الأقصى المسموح به هو {MAX_AUDIO_FILE_SIZE_MB} ميجابايت.",
            'voice_processing': "أواجه مشكلة في معالجة رسالتك الصوتية. يرجى المحاولة بإرسالها كنص بدلاً من ذلك.",
            'general': "يرجى التحقق من المدخلات والمحاولة مرة أخرى."
        }
    }

    messages = error_messages.get(language, error_messages['en'])

    # Determine error type based on field or message
    if error.field:
        field_lower = error.field.lower()
        if 'phone' in field_lower:
            return messages['phone']
        elif 'date' in field_lower:
            return messages['date']
        elif 'time' in field_lower:
            return messages['time']
        elif 'name' in field_lower:
            return messages['name']
        elif 'service' in field_lower:
            return messages['service']
        elif 'location' in field_lower:
            return messages['location']
        elif 'media' in field_lower or 'url' in field_lower:
            return messages['media_url']
        elif 'content' in field_lower or 'type' in field_lower:
            return messages['audio_format']

    # Check error message content for voice-related errors
    error_msg_lower = error.message.lower()
    if 'audio format' in error_msg_lower or 'unsupported' in error_msg_lower:
        return messages['audio_format']
    elif 'media' in error_msg_lower or 'url' in error_msg_lower:
        return messages['media_url']
    elif 'size' in error_msg_lower or 'large' in error_msg_lower:
        return messages['media_size']
    elif 'voice' in error_msg_lower or 'transcription' in error_msg_lower:
        return messages['voice_processing']

    return messages['general']
