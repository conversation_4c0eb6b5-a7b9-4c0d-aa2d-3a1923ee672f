# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Random password generation for database users
resource "random_password" "db_password" {
  for_each = local.deploy_project_ids
  length   = 32
  special  = true
}

# Cloud SQL PostgreSQL instances
resource "google_sql_database_instance" "postgres_main" {
  for_each         = local.deploy_project_ids
  name             = "${var.project_name}-postgres-${each.key}"
  database_version = "POSTGRES_15"
  region           = var.region
  project          = each.value

  settings {
    tier                        = each.key == "prod" ? var.database_tier_prod : var.database_tier_staging
    availability_type           = each.key == "prod" ? var.database_availability_prod : var.database_availability_staging
    disk_type                   = "PD_SSD"
    disk_size                   = each.key == "prod" ? var.database_disk_size_prod : var.database_disk_size_staging
    disk_autoresize            = true
    disk_autoresize_limit      = each.key == "prod" ? var.database_disk_limit_prod : var.database_disk_limit_staging

    backup_configuration {
      enabled                        = true
      start_time                     = "03:00"
      point_in_time_recovery_enabled = each.key == "prod"
      backup_retention_settings {
        retained_backups = each.key == "prod" ? var.database_backup_retention_prod : var.database_backup_retention_staging
      }
    }

    ip_configuration {
      ipv4_enabled    = true
      authorized_networks {
        name  = "cloud-build"
        value = "0.0.0.0/0"  # Will be restricted in Phase 2 network security
      }
    }

    database_flags {
      name  = "max_connections"
      value = each.key == "prod" ? var.database_max_connections_prod : var.database_max_connections_staging
    }

    database_flags {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements"
    }
  }

  deletion_protection = each.key == "prod"
  depends_on         = [google_project_service.shared_services]
}

# Database creation
resource "google_sql_database" "beauty_center" {
  for_each = local.deploy_project_ids
  name     = "beauty_center"
  instance = google_sql_database_instance.postgres_main[each.key].name
  project  = each.value
}

# Database user creation
resource "google_sql_user" "app_user" {
  for_each = local.deploy_project_ids
  name     = "${var.project_name}-app-user"
  instance = google_sql_database_instance.postgres_main[each.key].name
  password = random_password.db_password[each.key].result
  project  = each.value
}
