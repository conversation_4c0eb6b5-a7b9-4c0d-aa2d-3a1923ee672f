# Smart Booking System REST API

This module provides a comprehensive RESTful API for the Smart Booking System, enabling third-party services and AI agents to seamlessly integrate with the system.

## Table of Contents
- [Base URL and API Structure](#base-url-and-api-structure)
- [Authentication](#authentication)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [API Endpoints](#api-endpoints)
- [Data Types and Validation](#data-types-and-validation)
- [Testing](#testing)

## Base URL and API Structure

### Base URL Format
All API endpoints are relative to your Odoo instance base URL:
```
https://your-instance.odoo.sh/api/{endpoint}
```

### Complete URL Examples
- **Test API**: `GET https://your-instance.odoo.sh/api/test`
- **Get all bookings**: `GET https://your-instance.odoo.sh/api/booking`
- **Get specific booking**: `GET https://your-instance.odoo.sh/api/booking/123`
- **Create booking**: `POST https://your-instance.odoo.sh/api/booking`

## Authentication

All API endpoints (except `/api/test`) require authentication using an API key. The API key must be included in the request headers.

### Header Format
```http
api-key: YOUR_API_KEY
Content-Type: application/json
```

### API Key Generation

**Important**: Odoo 18 does not have the "Settings > Technical > API Keys" menu. Use one of these methods:

#### Method 1: Using the Provided Script (Recommended)
1. Access your Odoo shell or database interface
2. Run the script located at `rest_api/create_api_key.py`
3. Execute the `create_api_key_manual()` function:
   ```python
   # In Odoo shell:
   exec(open('rest_api/create_api_key.py').read())
   create_api_key_manual()
   ```
4. Copy the generated API key from the output

#### Method 2: Manual Database Creation
```python
# In Odoo shell:
admin_user = env['res.users'].search([('groups_id', 'in', env.ref('base.group_system').id)], limit=1)
api_key = env['ir.api.key'].create({
    'name': 'API Integration Key',
    'user_id': admin_user.id,
    'active': True,
})
print(f"API Key: {api_key.key}")
```

#### Method 3: Development Testing Key
For development and testing purposes only, you can use the temporary key:
```
TempTestKey123456789012345678901
```
**Warning**: This key should never be used in production environments.

## Response Format

All API responses follow a standardized JSON format:

### Successful Response Structure
```json
{
  "status": 200,
  "data": {
    // Response data here
  }
}
```

### Error Response Structure
```json
{
  "status": 400,
  "error": "Error message description"
}
```

### HTTP Status Codes
- **200**: Success (GET, PUT operations)
- **201**: Created (POST operations)
- **400**: Bad Request (validation errors, malformed JSON)
- **401**: Unauthorized (missing or invalid API key)
- **404**: Not Found (resource doesn't exist)
- **500**: Internal Server Error (server-side errors)

## Error Handling

The API provides detailed error information to help with debugging and integration.

### Common Error Scenarios

#### 1. Authentication Errors (401)
```json
{
  "status": 401,
  "error": "Missing API Key"
}
```
```json
{
  "status": 401,
  "error": "Invalid API Key"
}
```

#### 2. Validation Errors (400)
```json
{
  "status": 400,
  "error": "Missing required field: customer_id"
}
```
```json
{
  "status": 400,
  "error": "Invalid payload"
}
```

#### 3. Resource Not Found (404)
```json
{
  "status": 404,
  "error": "Booking not found"
}
```
```json
{
  "status": 404,
  "error": "Customer not found"
}
```

#### 4. Server Errors (500)
```json
{
  "status": 500,
  "error": "Error creating booking: [specific error details]"
}
```

### Error Handling Best Practices for AI Agents

1. **Always check the `status` field** in the response
2. **Parse error messages** for specific validation issues
3. **Implement retry logic** for 500 errors with exponential backoff
4. **Validate data locally** before sending to reduce 400 errors
5. **Cache API keys** and handle 401 errors by refreshing authentication

## API Endpoints

### Test Endpoint

#### Test API Connection
- **URL**: `/api/test`
- **Method**: `GET`
- **Authentication**: Not required
- **Description**: Verify API connectivity and get version information

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/test
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "message": "API is working!",
    "timestamp": "2023-06-15 14:30:25",
    "version": "1.0"
  }
}
```

### Bookings

#### Get all bookings
- **URL**: `/api/booking`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `customer_id` (integer, optional): Filter by customer ID
- `worker_id` (integer, optional): Filter by worker ID
- `state` (string, optional): Filter by state. Valid values: `draft`, `confirmed`, `done`, `cancelled`

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/booking?customer_id=5&state=confirmed
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 1,
      "name": "BOOK/2023/001",
      "customer_id": 5,
      "customer_name": "John Doe",
      "worker_id": 3,
      "worker_name": "Jane Smith",
      "service_id": 2,
      "service_name": "House Cleaning",
      "booking_date": "2023-06-15",
      "start_time": "2023-06-15 10:00:00",
      "end_time": "2023-06-15 12:00:00",
      "duration": 2.0,
      "price": 150.0,
      "state": "confirmed"
    }
  ]
}
```

**Empty Result Response:**
```json
{
  "status": 200,
  "data": []
}
```

#### Get a specific booking
- **URL**: `/api/booking/{booking_id}`
- **Method**: `GET`
- **Authentication**: Required

**Path Parameters:**
- `booking_id` (integer, required): The ID of the booking to retrieve

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/booking/1
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 1,
    "name": "BOOK/2023/001",
    "customer_id": 5,
    "customer_name": "John Doe",
    "worker_id": 3,
    "worker_name": "Jane Smith",
    "service_id": 2,
    "service_name": "House Cleaning",
    "booking_date": "2023-06-15",
    "start_time": "2023-06-15 10:00:00",
    "end_time": "2023-06-15 12:00:00",
    "duration": 2.0,
    "price": 150.0,
    "state": "confirmed",
    "skill_id": 4,
    "skill_name": "Residential Cleaning",
    "contract_id": 7
  }
}
```

**Error Response (Not Found):**
```json
{
  "status": 404,
  "error": "Booking not found"
}
```

#### Create a booking
- **URL**: `/api/booking`
- **Method**: `POST`
- **Authentication**: Required

**Request Body Fields:**
- `customer_id` (integer, required): ID of the customer
- `worker_id` (integer, required): ID of the assigned worker
- `service_id` (integer, required): ID of the service to be performed
- `booking_date` (string, required): Date in YYYY-MM-DD format
- `start_time` (string, required): Start datetime in YYYY-MM-DD HH:MM:SS format
- `end_time` (string, required): End datetime in YYYY-MM-DD HH:MM:SS format

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/booking
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "customer_id": 5,
  "worker_id": 3,
  "service_id": 2,
  "booking_date": "2023-06-15",
  "start_time": "2023-06-15 10:00:00",
  "end_time": "2023-06-15 12:00:00"
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 15,
    "name": "BOOK/2023/015",
    "customer_id": 5,
    "worker_id": 3,
    "service_id": 2,
    "booking_date": "2023-06-15",
    "start_time": "2023-06-15 10:00:00",
    "end_time": "2023-06-15 12:00:00",
    "state": "draft"
  }
}
```

**Validation Error Response:**
```json
{
  "status": 400,
  "error": "Missing required field: customer_id"
}
```

#### Update a booking
- **URL**: `/api/booking/{booking_id}`
- **Method**: `PUT`
- **Authentication**: Required

**Path Parameters:**
- `booking_id` (integer, required): The ID of the booking to update

**Request Body Fields (all optional):**
- `booking_date` (string): Date in YYYY-MM-DD format
- `start_time` (string): Start datetime in YYYY-MM-DD HH:MM:SS format
- `end_time` (string): End datetime in YYYY-MM-DD HH:MM:SS format
- `customer_id` (integer): ID of the customer
- `worker_id` (integer): ID of the assigned worker
- `service_id` (integer): ID of the service

**Example Request:**
```http
PUT https://your-instance.odoo.sh/api/booking/15
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "booking_date": "2023-06-16",
  "start_time": "2023-06-16 11:00:00",
  "end_time": "2023-06-16 13:00:00"
}
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "BOOK/2023/015",
    "customer_id": 5,
    "worker_id": 3,
    "service_id": 2,
    "booking_date": "2023-06-16",
    "start_time": "2023-06-16 11:00:00",
    "end_time": "2023-06-16 13:00:00",
    "state": "draft"
  }
}
```

#### Delete a booking
- **URL**: `/api/booking/{booking_id}`
- **Method**: `DELETE`
- **Authentication**: Required

**Path Parameters:**
- `booking_id` (integer, required): The ID of the booking to delete

**Example Request:**
```http
DELETE https://your-instance.odoo.sh/api/booking/15
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "message": "Booking deleted successfully"
  }
}
```

#### Confirm a booking
- **URL**: `/api/booking/{booking_id}/confirm`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `booking_id` (integer, required): The ID of the booking to confirm

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/booking/15/confirm
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "BOOK/2023/015",
    "state": "confirmed",
    "message": "Booking confirmed successfully"
  }
}
```

#### Cancel a booking
- **URL**: `/api/booking/{booking_id}/cancel`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `booking_id` (integer, required): The ID of the booking to cancel

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/booking/15/cancel
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "BOOK/2023/015",
    "state": "cancelled",
    "message": "Booking cancelled successfully"
  }
}
```

### Customers

#### Get all customers
- **URL**: `/api/customer`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `name` (string, optional): Filter by name (partial match, case-insensitive)
- `email` (string, optional): Filter by email (partial match, case-insensitive)
- `phone` (string, optional): Filter by phone (partial match)

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/customer?name=john&email=gmail
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 5,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "mobile": "+1987654321",
      "street": "123 Main St",
      "city": "New York",
      "zip": "10001",
      "country_id": 233,
      "country_name": "United States",
      "gps_coordinates": "40.7128,-74.0060",
      "contract_type": "single",
      "cumulative_rating": 4.5
    }
  ]
}
```

#### Get a specific customer
- **URL**: `/api/customer/{customer_id}`
- **Method**: `GET`
- **Authentication**: Required

**Path Parameters:**
- `customer_id` (integer, required): The ID of the customer to retrieve

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/customer/5
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 5,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "mobile": "+1987654321",
    "street": "123 Main St",
    "city": "New York",
    "zip": "10001",
    "country_id": 233,
    "country_name": "United States",
    "gps_coordinates": "40.7128,-74.0060",
    "contract_type": "single",
    "cumulative_rating": 4.5,
    "preferred_workers": [
      {
        "id": 3,
        "name": "Jane Smith"
      }
    ],
    "blocked_workers": [],
    "url_link": "https://customer-portal.example.com/john-doe"
  }
}
```

#### Create a customer
- **URL**: `/api/customer`
- **Method**: `POST`
- **Authentication**: Required

**Request Body Fields:**
- `name` (string, required): Customer's full name
- `email` (string, optional): Customer's email address
- `phone` (string, optional): Customer's phone number
- `mobile` (string, optional): Customer's mobile number
- `street` (string, optional): Street address
- `city` (string, optional): City
- `zip` (string, optional): ZIP/postal code
- `country_id` (integer, optional): Country ID
- `gps_coordinates` (string, optional): GPS coordinates in "latitude,longitude" format

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/customer
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "mobile": "+1987654321",
  "street": "123 Main St",
  "city": "New York",
  "zip": "10001",
  "gps_coordinates": "40.7128,-74.0060"
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 25,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "mobile": "+1987654321",
    "gps_coordinates": "40.7128,-74.0060"
  }
}
```

#### Update a customer
- **URL**: `/api/customer/{customer_id}`
- **Method**: `PUT`
- **Authentication**: Required

**Path Parameters:**
- `customer_id` (integer, required): The ID of the customer to update

**Request Body Fields (all optional):**
- `name` (string): Customer's full name
- `email` (string): Customer's email address
- `phone` (string): Customer's phone number
- `mobile` (string): Customer's mobile number
- `street` (string): Street address
- `city` (string): City
- `zip` (string): ZIP/postal code
- `gps_coordinates` (string): GPS coordinates in "latitude,longitude" format

**Example Request:**
```http
PUT https://your-instance.odoo.sh/api/customer/25
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "name": "John Smith",
  "email": "<EMAIL>",
  "phone": "+1234567899"
}
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 25,
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567899",
    "mobile": "+1987654321",
    "gps_coordinates": "40.7128,-74.0060"
  }
}
```

#### Add preferred worker
- **URL**: `/api/customer/{customer_id}/preferred_workers`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `customer_id` (integer, required): The ID of the customer

**Request Body Fields:**
- `worker_id` (integer, required): The ID of the worker to add as preferred

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/customer/25/preferred_workers
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "worker_id": 3
}
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "message": "Worker added to preferred list successfully"
  }
}
```

### Workers

#### Get all workers
- **URL**: `/api/worker`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `name` (string, optional): Filter by name (partial match, case-insensitive)
- `region_id` (integer, optional): Filter by region ID
- `availability_status` (string, optional): Filter by availability status. Valid values: `available`, `unavailable`
- `skill_id` (integer, optional): Filter by skill ID

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/worker?availability_status=available&region_id=1
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 3,
      "name": "Jane Smith",
      "work_email": "<EMAIL>",
      "work_phone": "+1234567890",
      "mobile_phone": "+1987654321",
      "region_id": 1,
      "region_name": "Downtown",
      "availability_status": "available",
      "cumulative_rating": 4.8,
      "skills": [
        {
          "id": 4,
          "name": "Residential Cleaning",
          "category": "cleaning"
        },
        {
          "id": 7,
          "name": "Deep Cleaning",
          "category": "cleaning"
        }
      ],
      "is_driver": true,
      "housing_unit_id": 12,
      "housing_unit_name": "Building A - Unit 205"
    }
  ]
}
```

#### Get a specific worker
- **URL**: `/api/worker/{worker_id}`
- **Method**: `GET`
- **Authentication**: Required

**Path Parameters:**
- `worker_id` (integer, required): The ID of the worker to retrieve

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/worker/3
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 3,
    "name": "Jane Smith",
    "work_email": "<EMAIL>",
    "work_phone": "+1234567890",
    "mobile_phone": "+1987654321",
    "region_id": 1,
    "region_name": "Downtown",
    "availability_status": "available",
    "cumulative_rating": 4.8,
    "skills": [
      {
        "id": 4,
        "name": "Residential Cleaning",
        "category": "cleaning"
      }
    ],
    "is_driver": true,
    "is_worker": true,
    "housing_unit_id": 12,
    "housing_unit_name": "Building A - Unit 205",
    "daily_schedule": "Monday-Friday: 8:00-17:00",
    "bookings": [
      {
        "id": 1,
        "name": "BOOK/2023/001",
        "customer_id": 5,
        "customer_name": "John Doe",
        "booking_date": "2023-06-15",
        "start_time": "2023-06-15 10:00:00",
        "end_time": "2023-06-15 12:00:00",
        "state": "confirmed"
      }
    ]
  }
}
```

### Contracts

#### Get all contracts
- **URL**: `/api/contract`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `customer_id` (integer, optional): Filter by customer ID
- `worker_id` (integer, optional): Filter by worker ID
- `state` (string, optional): Filter by state. Valid values: `draft`, `confirmed`, `invoiced`, `cancelled`
- `contract_type` (string, optional): Filter by contract type. Valid values: `none`, `single`, `multiple`

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/contract?state=confirmed&contract_type=multiple
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 7,
      "name": "CONTRACT/2023/007",
      "customer_id": 5,
      "customer_name": "John Doe",
      "worker_id": 3,
      "worker_name": "Jane Smith",
      "start_time": "2023-06-15 10:00:00",
      "end_time": "2023-07-15 10:00:00",
      "total_price": 1500.0,
      "state": "confirmed",
      "contract_type": "multiple",
      "duration_in_months": 1,
      "is_invoiced": false,
      "visit_dates": [
        "2023-06-15",
        "2023-06-22",
        "2023-06-29",
        "2023-07-06"
      ]
    }
  ]
}
```

#### Get a specific contract
- **URL**: `/api/contract/{contract_id}`
- **Method**: `GET`
- **Authentication**: Required

**Path Parameters:**
- `contract_id` (integer, required): The ID of the contract to retrieve

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/contract/7
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 7,
    "name": "CONTRACT/2023/007",
    "customer_id": 5,
    "customer_name": "John Doe",
    "worker_id": 3,
    "worker_name": "Jane Smith",
    "start_time": "2023-06-15 10:00:00",
    "end_time": "2023-07-15 10:00:00",
    "total_price": 1500.0,
    "state": "confirmed",
    "contract_type": "multiple",
    "duration_in_months": 1,
    "is_invoiced": false,
    "visit_dates": [
      "2023-06-15",
      "2023-06-22",
      "2023-06-29",
      "2023-07-06"
    ],
    "invoices": []
  }
}
```

#### Create a contract
- **URL**: `/api/contract`
- **Method**: `POST`
- **Authentication**: Required

**Request Body Fields:**
- `customer_id` (integer, required): ID of the customer
- `worker_id` (integer, required): ID of the assigned worker
- `start_time` (string, required): Contract start datetime in YYYY-MM-DD HH:MM:SS format
- `end_time` (string, required): Contract end datetime in YYYY-MM-DD HH:MM:SS format
- `contract_type` (string, optional): Contract type. Valid values: `none`, `single`, `multiple`. Default: `none`

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/contract
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "customer_id": 5,
  "worker_id": 3,
  "start_time": "2023-06-15 10:00:00",
  "end_time": "2023-07-15 10:00:00",
  "contract_type": "multiple"
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 15,
    "name": "CONTRACT/2023/015",
    "customer_id": 5,
    "worker_id": 3,
    "start_time": "2023-06-15 10:00:00",
    "end_time": "2023-07-15 10:00:00",
    "state": "draft"
  }
}
```

#### Confirm a contract
- **URL**: `/api/contract/{contract_id}/confirm`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `contract_id` (integer, required): The ID of the contract to confirm

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/contract/15/confirm
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "CONTRACT/2023/015",
    "state": "confirmed",
    "message": "Contract confirmed successfully"
  }
}
```

#### Cancel a contract
- **URL**: `/api/contract/{contract_id}/cancel`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `contract_id` (integer, required): The ID of the contract to cancel

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/contract/15/cancel
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "CONTRACT/2023/015",
    "state": "cancelled",
    "message": "Contract cancelled successfully"
  }
}
```

#### Create an invoice for a contract
- **URL**: `/api/contract/{contract_id}/create_invoice`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `contract_id` (integer, required): The ID of the contract to invoice

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/contract/15/create_invoice
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 15,
    "name": "CONTRACT/2023/015",
    "state": "invoiced",
    "is_invoiced": true,
    "invoice_id": 45,
    "message": "Invoice created successfully"
  }
}
```

### Wallets

#### Get all wallets
- **URL**: `/api/wallet`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `partner_id` (integer, optional): Filter by partner (customer) ID

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/wallet?partner_id=5
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 12,
      "name": "John's Wallet",
      "partner_id": 5,
      "partner_name": "John Doe",
      "balance": 250.0,
      "currency_id": 1,
      "currency_name": "USD",
      "transactions": [
        {
          "id": 45,
          "amount": 100.0,
          "type": "payment",
          "date": "2023-06-15 14:30:00"
        },
        {
          "id": 46,
          "amount": 150.0,
          "type": "deposit",
          "date": "2023-06-14 10:15:00"
        }
      ]
    }
  ]
}
```

#### Get a specific wallet
- **URL**: `/api/wallet/{wallet_id}`
- **Method**: `GET`
- **Authentication**: Required

**Path Parameters:**
- `wallet_id` (integer, required): The ID of the wallet to retrieve

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/wallet/12
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": {
    "id": 12,
    "name": "John's Wallet",
    "partner_id": 5,
    "partner_name": "John Doe",
    "balance": 250.0,
    "currency_id": 1,
    "currency_name": "USD",
    "transactions": [
      {
        "id": 45,
        "amount": 100.0,
        "type": "payment",
        "date": "2023-06-15 14:30:00"
      }
    ]
  }
}
```

#### Create a wallet
- **URL**: `/api/wallet`
- **Method**: `POST`
- **Authentication**: Required

**Request Body Fields:**
- `name` (string, required): Wallet name
- `partner_id` (integer, required): ID of the partner (customer) who owns the wallet

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/wallet
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "name": "John's Wallet",
  "partner_id": 5
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 25,
    "name": "John's Wallet",
    "partner_id": 5,
    "partner_name": "John Doe",
    "balance": 0.0,
    "currency_id": 1,
    "currency_name": "USD"
  }
}
```

#### Create a wallet transaction
- **URL**: `/api/wallet/{wallet_id}/transaction`
- **Method**: `POST`
- **Authentication**: Required

**Path Parameters:**
- `wallet_id` (integer, required): The ID of the wallet

**Request Body Fields:**
- `amount` (float, required): Transaction amount (positive for deposits, negative for payments)
- `type` (string, required): Transaction type. Valid values: `payment`, `deposit`, `refund`

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/wallet/25/transaction
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "amount": 100.0,
  "type": "deposit"
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 67,
    "wallet_id": 25,
    "amount": 100.0,
    "type": "deposit",
    "date": "2023-06-15 16:45:00",
    "wallet_balance": 100.0
  }
}
```

### Ratings

#### Get all ratings
- **URL**: `/api/rating`
- **Method**: `GET`
- **Authentication**: Required

**Query Parameters:**
- `customer_id` (integer, optional): Filter by customer ID
- `worker_id` (integer, optional): Filter by worker ID
- `booking_id` (integer, optional): Filter by booking ID
- `rating` (string, optional): Filter by rating value. Valid values: `1`, `2`, `3`, `4`, `5`

**Example Request:**
```http
GET https://your-instance.odoo.sh/api/rating?worker_id=3&rating=5
api-key: YOUR_API_KEY
```

**Example Response:**
```json
{
  "status": 200,
  "data": [
    {
      "id": 23,
      "customer_id": 5,
      "customer_name": "John Doe",
      "worker_id": 3,
      "worker_name": "Jane Smith",
      "booking_id": 1,
      "booking_name": "BOOK/2023/001",
      "rating": "5",
      "comments": "Excellent service! Very professional and thorough.",
      "ticket_count": 0
    }
  ]
}
```

#### Create a rating
- **URL**: `/api/rating`
- **Method**: `POST`
- **Authentication**: Required

**Request Body Fields:**
- `customer_id` (integer, required): ID of the customer giving the rating
- `worker_id` (integer, required): ID of the worker being rated
- `booking_id` (integer, required): ID of the booking being rated
- `rating` (string, required): Rating value. Valid values: `1`, `2`, `3`, `4`, `5`
- `comments` (string, optional): Additional comments about the service

**Example Request:**
```http
POST https://your-instance.odoo.sh/api/rating
Content-Type: application/json
api-key: YOUR_API_KEY

{
  "customer_id": 5,
  "worker_id": 3,
  "booking_id": 1,
  "rating": "5",
  "comments": "Excellent service! Very professional and thorough."
}
```

**Example Response:**
```json
{
  "status": 201,
  "data": {
    "id": 35,
    "customer_id": 5,
    "worker_id": 3,
    "booking_id": 1,
    "rating": "5",
    "comments": "Excellent service! Very professional and thorough."
  }
}
```

## Data Types and Validation

### Field Types and Formats

#### Date and DateTime Fields
- **Date fields** (e.g., `booking_date`): Use `YYYY-MM-DD` format
  - Example: `"2023-06-15"`
- **DateTime fields** (e.g., `start_time`, `end_time`): Use `YYYY-MM-DD HH:MM:SS` format
  - Example: `"2023-06-15 10:00:00"`

#### Numeric Fields
- **Integer fields** (e.g., `customer_id`, `worker_id`): Whole numbers
  - Example: `5`, `123`
- **Float fields** (e.g., `price`, `amount`): Decimal numbers
  - Example: `150.0`, `99.99`

#### String Fields
- **Text fields**: UTF-8 encoded strings
- **Email fields**: Must be valid email format
- **Phone fields**: Any string format accepted (no validation)
- **GPS coordinates**: Format as `"latitude,longitude"`
  - Example: `"40.7128,-74.0060"`

#### Enum Fields
- **Booking states**: `draft`, `confirmed`, `done`, `cancelled`
- **Contract states**: `draft`, `confirmed`, `invoiced`, `cancelled`
- **Contract types**: `none`, `single`, `multiple`
- **Availability status**: `available`, `unavailable`
- **Transaction types**: `payment`, `deposit`, `refund`
- **Rating values**: `1`, `2`, `3`, `4`, `5` (as strings)

### Required vs Optional Fields

#### Booking Creation
- **Required**: `customer_id`, `worker_id`, `service_id`, `booking_date`, `start_time`, `end_time`
- **Optional**: All other fields are auto-generated or optional

#### Customer Creation
- **Required**: `name`
- **Optional**: `email`, `phone`, `mobile`, `street`, `city`, `zip`, `country_id`, `gps_coordinates`

#### Contract Creation
- **Required**: `customer_id`, `worker_id`, `start_time`, `end_time`
- **Optional**: `contract_type` (defaults to `none`)

#### Wallet Creation
- **Required**: `name`, `partner_id`
- **Optional**: All other fields are auto-generated

#### Rating Creation
- **Required**: `customer_id`, `worker_id`, `booking_id`, `rating`
- **Optional**: `comments`

### Field Length Limits
- **Name fields**: 255 characters maximum
- **Email fields**: 254 characters maximum
- **Phone fields**: 32 characters maximum
- **Comments**: 1000 characters maximum
- **GPS coordinates**: 50 characters maximum

## AI Agent Integration Guidelines

### Best Practices for Automated Systems

1. **Always validate data locally** before sending requests to reduce API errors
2. **Use the test endpoint** (`/api/test`) to verify connectivity before starting operations
3. **Implement proper error handling** for all possible HTTP status codes
4. **Cache API keys** securely and handle authentication errors gracefully
5. **Use appropriate timeouts** for HTTP requests (recommended: 30 seconds)
6. **Implement retry logic** with exponential backoff for 500 errors
7. **Validate enum values** before sending to ensure they match accepted values
8. **Parse response data structure** correctly using the `status` and `data` fields

### Common Integration Patterns

#### Creating a Complete Booking Flow
1. Get available workers: `GET /api/worker?availability_status=available`
2. Get customer details: `GET /api/customer/{customer_id}`
3. Create booking: `POST /api/booking`
4. Confirm booking: `POST /api/booking/{booking_id}/confirm`

#### Handling Pagination (Future Enhancement)
Currently, all endpoints return complete result sets. For large datasets, consider implementing client-side filtering using query parameters.

#### Error Recovery Strategies
- **401 Unauthorized**: Refresh API key and retry
- **404 Not Found**: Verify resource IDs exist before operations
- **400 Bad Request**: Check field validation and retry with corrected data
- **500 Server Error**: Implement exponential backoff retry (max 3 attempts)

## Testing

### Postman Collection
Use the provided Postman collection (`custom_odoo_api_tests.json`) to test all API endpoints:

1. Import the collection into Postman
2. Set environment variables:
   - `base_url`: Your Odoo instance URL (e.g., `https://your-instance.odoo.sh`)
   - `api_key`: Your generated API key
3. Run the collection to test all endpoints

### Manual Testing with cURL

#### Test API Connection
```bash
curl -X GET "https://your-instance.odoo.sh/api/test"
```

#### Test Authentication
```bash
curl -X GET "https://your-instance.odoo.sh/api/booking" \
  -H "api-key: YOUR_API_KEY"
```

#### Create a Booking
```bash
curl -X POST "https://your-instance.odoo.sh/api/booking" \
  -H "Content-Type: application/json" \
  -H "api-key: YOUR_API_KEY" \
  -d '{
    "customer_id": 5,
    "worker_id": 3,
    "service_id": 2,
    "booking_date": "2023-06-15",
    "start_time": "2023-06-15 10:00:00",
    "end_time": "2023-06-15 12:00:00"
  }'
```

## Odoo.sh Compatibility

This API is designed to work seamlessly with Odoo.sh hosting:
- Uses Odoo's built-in HTTP layer for optimal performance
- Avoids long-running processes that could timeout
- Handles SSL certificates automatically
- Works within Odoo.sh's resource constraints
- Compatible with Odoo.sh's load balancing and scaling

## Support and Troubleshooting

### Common Issues

1. **"Missing API Key" Error**: Ensure the `api-key` header is included in all requests
2. **"Invalid API Key" Error**: Verify the API key was generated correctly and is active
3. **"Invalid payload" Error**: Check JSON formatting and ensure Content-Type header is set
4. **"Resource not found" Errors**: Verify that referenced IDs (customer_id, worker_id, etc.) exist in the system

### Getting Help

For additional support or questions about the API:
1. Check the error message details in the response
2. Verify your request format matches the examples in this documentation
3. Test with the provided Postman collection to isolate issues
4. Ensure your Odoo instance is accessible and the Smart Booking System module is installed
