"""
Google Cloud Speech-to-Text integration for voice message transcription.

This module provides comprehensive speech-to-text functionality with support for
multiple audio formats, language detection, and robust error handling following
the established patterns from the existing codebase.
"""

import os
import logging
from typing import Optional, Dict, Any, Tu<PERSON>
from dataclasses import dataclass
from pathlib import Path
import time

# Google Cloud Speech-to-Text imports
try:
    from google.cloud import speech
    from google.cloud.speech import RecognitionConfig, RecognitionAudio
    from google.api_core import exceptions as gcp_exceptions
    SPEECH_API_AVAILABLE = True
except ImportError:
    SPEECH_API_AVAILABLE = False
    speech = None
    RecognitionConfig = None
    RecognitionAudio = None
    gcp_exceptions = None

from app.utils.exceptions import (
    ExternalAPIException, ErrorContext, handle_external_api_error, log_exception,
    ErrorSeverity, ErrorCategory
)
from app.utils.validation import SUPPORTED_AUDIO_FORMATS, MAX_AUDIO_FILE_SIZE_MB
from app.utils.language import detect_language

logger = logging.getLogger(__name__)

# Speech-to-Text configuration constants
DEFAULT_SAMPLE_RATE = 16000  # Default sample rate for audio processing
MAX_TRANSCRIPTION_TIMEOUT = 60  # Maximum timeout for transcription in seconds
MIN_CONFIDENCE_THRESHOLD = 0.5  # Minimum confidence score to accept transcription
MAX_RETRY_ATTEMPTS = 3  # Maximum retry attempts for API failures

@dataclass
class TranscriptionResult:
    """Result of a speech-to-text transcription operation."""
    success: bool
    transcript: str = ""
    confidence: float = 0.0
    language_code: str = ""
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    processing_time: float = 0.0

class SpeechToTextService:
    """
    Google Cloud Speech-to-Text service for transcribing voice messages.
    
    Provides comprehensive transcription capabilities with support for multiple
    audio formats, language detection, and robust error handling following
    the established patterns from the existing codebase.
    """
    
    def __init__(self):
        """Initialize the Speech-to-Text service with proper authentication."""
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT_ID')
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """
        Initialize Google Cloud Speech-to-Text client with proper error handling.
        """
        try:
            if not SPEECH_API_AVAILABLE:
                raise ImportError("Google Cloud Speech-to-Text library not available")
            
            if not self.project_id:
                raise ValueError("GOOGLE_CLOUD_PROJECT_ID environment variable not set")
            
            # Initialize client with project ID
            self.client = speech.SpeechClient()
            logger.info("Speech-to-Text client initialized successfully")
            
        except Exception as e:
            error_msg = f"Failed to initialize Speech-to-Text client: {str(e)}"
            logger.error(error_msg)
            
            # Create structured exception
            api_exception = ExternalAPIException(
                message=error_msg,
                service="Google Cloud Speech-to-Text",
                user_message="Speech recognition service is currently unavailable",
                severity=ErrorSeverity.HIGH,
                category=ErrorCategory.EXTERNAL_API,
                context=ErrorContext(
                    operation="initialize_speech_client",
                    additional_data={
                        "project_id": self.project_id,
                        "api_available": SPEECH_API_AVAILABLE
                    }
                ),
                original_exception=e
            )
            log_exception(api_exception)
            raise api_exception
    
    def transcribe_audio(
        self, 
        audio_file_path: str, 
        content_type: str,
        language_hint: Optional[str] = None
    ) -> TranscriptionResult:
        """
        Transcribe audio file to text using Google Cloud Speech-to-Text API.
        
        Args:
            audio_file_path: Path to the audio file to transcribe
            content_type: MIME type of the audio file
            language_hint: Optional language hint for better accuracy
            
        Returns:
            TranscriptionResult with transcription status and data
        """
        start_time = time.time()
        result = TranscriptionResult(success=False)
        
        try:
            # Validate inputs
            validation_error = self._validate_transcription_inputs(audio_file_path, content_type)
            if validation_error:
                result.error_message = validation_error
                return result
            
            # Detect language if not provided
            if not language_hint:
                language_hint = self._detect_language_for_transcription()
            
            # Log transcription attempt
            logger.info(f"Starting transcription: file={Path(audio_file_path).name}, "
                       f"type={content_type}, language={language_hint}")
            
            # Attempt transcription with retry logic
            for attempt in range(1, MAX_RETRY_ATTEMPTS + 1):
                try:
                    result = self._attempt_transcription(
                        audio_file_path, content_type, language_hint, attempt
                    )
                    
                    if result.success:
                        result.processing_time = time.time() - start_time
                        logger.info(f"Transcription successful on attempt {attempt}: "
                                  f"confidence={result.confidence:.2f}, "
                                  f"length={len(result.transcript)} chars, "
                                  f"time={result.processing_time:.2f}s")
                        return result
                    elif attempt < MAX_RETRY_ATTEMPTS:
                        logger.warning(f"Transcription attempt {attempt} failed: {result.error_message}. Retrying...")
                        time.sleep(1 * attempt)  # Exponential backoff
                        continue
                    else:
                        logger.error(f"Transcription failed after {MAX_RETRY_ATTEMPTS} attempts: {result.error_message}")
                        return result
                        
                except Exception as e:
                    error_msg = f"Transcription attempt {attempt} failed with exception: {str(e)}"
                    logger.error(error_msg)
                    
                    if attempt < MAX_RETRY_ATTEMPTS:
                        time.sleep(1 * attempt)  # Exponential backoff
                        continue
                    else:
                        # Final attempt failed
                        api_exception = handle_external_api_error(
                            service="Google Cloud Speech-to-Text",
                            operation="transcribe_audio",
                            original_exception=e,
                            context=ErrorContext(
                                operation="audio_transcription",
                                additional_data={
                                    "audio_file": Path(audio_file_path).name,
                                    "content_type": content_type,
                                    "language_hint": language_hint,
                                    "attempt": attempt
                                }
                            )
                        )
                        log_exception(api_exception)
                        result.error_message = f"Transcription failed after {MAX_RETRY_ATTEMPTS} attempts: {str(e)}"
                        result.processing_time = time.time() - start_time
                        return result
                        
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unexpected error in transcription: {e}")
            result.error_message = f"Unexpected transcription error: {str(e)}"
            result.processing_time = time.time() - start_time
            return result
    
    def _validate_transcription_inputs(self, audio_file_path: str, content_type: str) -> Optional[str]:
        """
        Validate transcription inputs for security and compatibility.
        
        Args:
            audio_file_path: Path to audio file
            content_type: MIME type of audio file
            
        Returns:
            Error message if validation fails, None if valid
        """
        # Check if client is initialized
        if not self.client:
            return "Speech-to-Text client not initialized"
        
        # Validate file path
        if not audio_file_path or not isinstance(audio_file_path, str):
            return "Invalid audio file path"
        
        # Check file exists and is readable
        file_path = Path(audio_file_path)
        if not file_path.exists():
            return f"Audio file not found: {file_path.name}"
        
        if not file_path.is_file():
            return f"Path is not a file: {file_path.name}"
        
        # Validate file size
        file_size = file_path.stat().st_size
        max_size_bytes = MAX_AUDIO_FILE_SIZE_MB * 1024 * 1024
        if file_size > max_size_bytes:
            return f"Audio file too large: {file_size} bytes (max: {max_size_bytes})"
        
        if file_size == 0:
            return "Audio file is empty"
        
        # Validate content type
        if content_type not in SUPPORTED_AUDIO_FORMATS:
            return f"Unsupported audio format: {content_type}"
        
        # Security check - prevent directory traversal
        try:
            file_path.resolve(strict=True)
        except (OSError, RuntimeError):
            return "Invalid file path - potential security issue"
        
        return None
    
    def _detect_language_for_transcription(self) -> str:
        """
        Detect appropriate language code for transcription.
        
        Uses the existing language detection patterns from the codebase.
        
        Returns:
            Language code for Speech-to-Text API (en-US or ar-SA)
        """
        # Default to English if no context available
        # In a real implementation, this could use user context or previous messages
        # For now, we'll use the established pattern from the language module
        
        # Map internal language codes to Speech-to-Text API language codes
        language_mapping = {
            'en': 'en-US',  # English (US)
            'ar': 'ar-SA'   # Arabic (Saudi Arabia)
        }
        
        # Default to English for voice messages
        # This could be enhanced to use user context in future iterations
        default_language = 'en'
        
        return language_mapping.get(default_language, 'en-US')
    
    def _get_audio_encoding(self, content_type: str):
        """
        Get appropriate audio encoding for Speech-to-Text API based on content type.

        Args:
            content_type: MIME type of the audio file

        Returns:
            AudioEncoding enum value for the API, or None if API not available
        """
        if not SPEECH_API_AVAILABLE or not speech:
            return None

        encoding_mapping = {
            'audio/ogg': speech.RecognitionConfig.AudioEncoding.OGG_OPUS,
            'audio/mp3': speech.RecognitionConfig.AudioEncoding.MP3,
            'audio/mpeg': speech.RecognitionConfig.AudioEncoding.MP3,
            'audio/mp4': speech.RecognitionConfig.AudioEncoding.MP3,  # MP4 audio often uses MP3
            'audio/3gpp': speech.RecognitionConfig.AudioEncoding.AMR,
            'audio/amr': speech.RecognitionConfig.AudioEncoding.AMR,
            'audio/amr-nb': speech.RecognitionConfig.AudioEncoding.AMR_NB,
            'audio/webm': speech.RecognitionConfig.AudioEncoding.WEBM_OPUS
        }

        return encoding_mapping.get(
            content_type.lower(),
            speech.RecognitionConfig.AudioEncoding.ENCODING_UNSPECIFIED
        )

    def _attempt_transcription(
        self,
        audio_file_path: str,
        content_type: str,
        language_code: str,
        attempt: int
    ) -> TranscriptionResult:
        """
        Attempt a single transcription operation with full error handling.

        Args:
            audio_file_path: Path to the audio file
            content_type: MIME type of the audio file
            language_code: Language code for transcription
            attempt: Current attempt number (for logging)

        Returns:
            TranscriptionResult with transcription status and data
        """
        result = TranscriptionResult(success=False, language_code=language_code)

        try:
            # Read audio file
            with open(audio_file_path, 'rb') as audio_file:
                audio_content = audio_file.read()

            # Create audio object
            audio = RecognitionAudio(content=audio_content)

            # Configure recognition settings
            config = RecognitionConfig(
                encoding=self._get_audio_encoding(content_type),
                sample_rate_hertz=DEFAULT_SAMPLE_RATE,
                language_code=language_code,
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                max_alternatives=1,
                model='latest_long'  # Use latest long-form model for better accuracy
            )

            # Perform transcription
            logger.debug(f"Sending transcription request (attempt {attempt}): "
                        f"encoding={config.encoding.name}, language={language_code}")

            response = self.client.recognize(config=config, audio=audio)

            # Process response
            if not response.results:
                result.error_message = "No transcription results returned from API"
                return result

            # Get the best alternative
            alternative = response.results[0].alternatives[0]
            transcript = alternative.transcript.strip()
            confidence = alternative.confidence

            # Validate transcription quality
            if confidence < MIN_CONFIDENCE_THRESHOLD:
                result.error_message = f"Low confidence transcription: {confidence:.2f} < {MIN_CONFIDENCE_THRESHOLD}"
                result.transcript = transcript  # Still include the transcript for debugging
                result.confidence = confidence
                return result

            if not transcript:
                result.error_message = "Empty transcription result"
                return result

            # Create metadata
            metadata = {
                'api_response_results_count': len(response.results),
                'word_count': len(transcript.split()),
                'character_count': len(transcript),
                'attempt_number': attempt,
                'encoding_used': config.encoding.name,
                'sample_rate': config.sample_rate_hertz,
                'model_used': config.model
            }

            # Add word-level confidence if available
            if hasattr(alternative, 'words') and alternative.words:
                word_confidences = [word.confidence for word in alternative.words if hasattr(word, 'confidence')]
                if word_confidences:
                    metadata['word_confidence_avg'] = sum(word_confidences) / len(word_confidences)
                    metadata['word_confidence_min'] = min(word_confidences)
                    metadata['word_count_with_confidence'] = len(word_confidences)

            # Success
            result.success = True
            result.transcript = transcript
            result.confidence = confidence
            result.metadata = metadata

            return result

        except gcp_exceptions.InvalidArgument as e:
            result.error_message = f"Invalid transcription request: {str(e)}"
            return result
        except gcp_exceptions.DeadlineExceeded as e:
            result.error_message = f"Transcription timeout: {str(e)}"
            return result
        except gcp_exceptions.ResourceExhausted as e:
            result.error_message = f"API quota exceeded: {str(e)}"
            return result
        except gcp_exceptions.PermissionDenied as e:
            result.error_message = f"Permission denied: {str(e)}"
            return result
        except gcp_exceptions.GoogleAPICallError as e:
            result.error_message = f"Google API error: {str(e)}"
            return result
        except Exception as e:
            result.error_message = f"Unexpected transcription error: {str(e)}"
            return result

    def is_service_available(self) -> bool:
        """
        Check if the Speech-to-Text service is available and properly configured.

        Returns:
            True if service is available, False otherwise
        """
        return (
            SPEECH_API_AVAILABLE and
            self.client is not None and
            self.project_id is not None
        )

    def get_supported_languages(self) -> Dict[str, str]:
        """
        Get supported languages for transcription.

        Returns:
            Dictionary mapping language codes to language names
        """
        return {
            'en-US': 'English (United States)',
            'ar-SA': 'Arabic (Saudi Arabia)'
        }

    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the Speech-to-Text service configuration.

        Returns:
            Dictionary with service configuration information
        """
        return {
            'service_available': self.is_service_available(),
            'api_library_available': SPEECH_API_AVAILABLE,
            'project_id': self.project_id,
            'supported_formats': SUPPORTED_AUDIO_FORMATS,
            'supported_languages': self.get_supported_languages(),
            'max_file_size_mb': MAX_AUDIO_FILE_SIZE_MB,
            'default_sample_rate': DEFAULT_SAMPLE_RATE,
            'min_confidence_threshold': MIN_CONFIDENCE_THRESHOLD,
            'max_timeout_seconds': MAX_TRANSCRIPTION_TIMEOUT,
            'max_retry_attempts': MAX_RETRY_ATTEMPTS
        }

# Convenience function for easy integration
def transcribe_voice_message(
    audio_file_path: str,
    content_type: str,
    language_hint: Optional[str] = None
) -> TranscriptionResult:
    """
    Convenience function to transcribe a voice message.

    Args:
        audio_file_path: Path to the audio file to transcribe
        content_type: MIME type of the audio file
        language_hint: Optional language hint for better accuracy

    Returns:
        TranscriptionResult with transcription status and data
    """
    try:
        service = SpeechToTextService()
        return service.transcribe_audio(audio_file_path, content_type, language_hint)
    except Exception as e:
        # Return error result if service initialization fails
        return TranscriptionResult(
            success=False,
            error_message=f"Failed to initialize transcription service: {str(e)}"
        )
