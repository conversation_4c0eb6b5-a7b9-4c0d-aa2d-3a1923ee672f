import os
from datetime import datetime
from typing import Optional, Dict
from langchain_core.tools import tool
import re
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

from app.utils.database import DatabaseManager, Appointment, create_database_connection
from app.utils.twilio_client import TwilioClient
from app.utils.odoo_integration import OdooIntegration
from app.utils.language import translate_response  # Import translate_response
from app.utils.validation import (
    AppointmentInput, ValidationError, sanitize_text_input,
    log_validation_failure, create_validation_response
)
from app.utils.exceptions import (
    ValidationException, BusinessLogicException, ExternalAPIException,
    ErrorContext, log_exception, sanitize_error_for_user,
    create_standardized_error_response, create_standardized_success_response
)

db = DatabaseManager()
twilio = TwilioClient()
# Lazy initialization for Odoo to ensure environment variables are loaded
_odoo_instance = None

def get_odoo_integration():
    """Get or create OdooIntegration instance with lazy initialization."""
    global _odoo_instance
    if _odoo_instance is None:
        try:
            _odoo_instance = OdooIntegration()
            logger.info("OdooIntegration initialized successfully in tools.py")
        except Exception as e:
            logger.error(f"Failed to initialize OdooIntegration in tools.py: {str(e)}")
            _odoo_instance = None
    return _odoo_instance

AVAILABLE_SERVICES = [
    "massage",
    "cleaning",
    "facial",
    "waxing"
]

SERVICE_NAME_MAP = {
    "cleaning": {"en": "cleaning", "ar": "تنظيف"},
    "facial": {"en": "facial", "ar": "عناية بالوجه"},
    "massage": {"en": "massage", "ar": "تدليك"},
    "waxing": {"en": "waxing", "ar": "إزالة الشعر"}
}

booking_contexts: Dict[str, Dict] = {}

logger = logging.getLogger(__name__)

def get_booking_context(phone_number: str) -> Dict:
    """Get or create booking context for a user."""
    if phone_number not in booking_contexts:
        booking_contexts[phone_number] = {
            "client_name": None,
            "service_type": None,
            "appointment_date": None,
            "appointment_time": None,
            "step": "start",
            "language": "en"
        }
    return booking_contexts[phone_number]

def clear_booking_context(phone_number: str):
    """Clear booking context for a user."""
    if phone_number in booking_contexts:
        del booking_contexts[phone_number]

def extract_date(text: str) -> Optional[str]:
    """Extract date from text in YYYY-MM-DD format."""
    date_pattern = r'\d{4}-\d{2}-\d{2}'
    match = re.search(date_pattern, text)
    if match:
        return match.group()
    return None

def extract_time(text: str) -> Optional[str]:
    """Extract time from text in HH:MM format."""
    time_pattern = r'\d{2}:\d{2}'
    match = re.search(time_pattern, text)
    if match:
        return match.group()
    return None

@tool
def book_appointment(
    client_name: str,
    phone_number: str,
    service_type: str,
    appointment_date: str,
    appointment_time: str,
    location: str = None,
    duration: int = None,
    is_recurring: bool = False,
    recurrence_pattern: str = None
) -> str:
    """Book an appointment for a client. Returns confirmation message or error."""
    try:
        language = get_booking_context(phone_number).get('language', 'en')

        # Comprehensive input validation using Pydantic model
        try:
            validated_input = AppointmentInput(
                client_name=client_name,
                phone_number=phone_number,
                service_type=service_type,
                appointment_date=appointment_date,
                appointment_time=appointment_time,
                location=location,
                duration=duration
            )

            # Use validated and sanitized inputs
            client_name = validated_input.client_name
            phone_number = validated_input.phone_number
            service_type = validated_input.service_type
            appointment_date = validated_input.appointment_date
            appointment_time = validated_input.appointment_time
            location = validated_input.location
            duration = validated_input.duration

        except Exception as validation_error:
            # Convert to ValidationException for standardized handling
            context = ErrorContext(
                user_id=phone_number,
                operation="book_appointment_validation",
                additional_data={"service_type": service_type}
            )

            if isinstance(validation_error, ValidationError):
                # Convert legacy ValidationError to ValidationException
                validation_exception = ValidationException(
                    message=str(validation_error),
                    field=getattr(validation_error, 'field', None),
                    context=context
                )
            else:
                validation_exception = ValidationException(
                    message="Invalid appointment input",
                    context=context
                )

            log_exception(validation_exception)
            response = create_validation_response(validation_exception, language)
            return translate_response(response, language)

        logger.info(f"\nAttempting to store appointment in database:")
        logger.info(f"Name: {client_name}")
        logger.info(f"Phone: {phone_number}")
        logger.info(f"Service: {service_type}")
        logger.info(f"Date: {appointment_date}")
        logger.info(f"Time: {appointment_time}")
        logger.info(f"Location: {location}")
        logger.info(f"Duration: {duration}")
        logger.info(f"Is Recurring: {is_recurring}")
        logger.info(f"Recurrence Pattern: {recurrence_pattern}")
        logger.info(f"Language: {language}")

        # Parse validated date and time
        try:
            date_obj = datetime.strptime(appointment_date, "%Y-%m-%d")
            time_obj = datetime.strptime(appointment_time, "%H:%M")
            logger.info(f"Parsed date: {date_obj}, time: {time_obj}")
        except ValueError as e:
            logger.error(f"Error parsing date/time: {str(e)}")
            response = "Error: Invalid date or time format. Please use YYYY-MM-DD for date and HH:MM for time."
            return translate_response(response, language)

        # Use consolidated database operation from DatabaseManager
        existing_appointments = db.get_appointments_by_datetime(date_obj, appointment_time)
        if existing_appointments:
            # Use BusinessLogicException for booking conflicts
            existing_appointment = existing_appointments[0]  # Get first conflicting appointment
            conflict_exception = BusinessLogicException(
                message=f"Time slot {appointment_date} {appointment_time} already booked",
                rule="no_double_booking",
                user_message=f"Sorry, the time slot {appointment_time} on {appointment_date} is already booked. Please choose another time.",
                context=ErrorContext(
                    user_id=phone_number,
                    operation="check_time_slot_availability",
                    additional_data={
                        "requested_date": appointment_date,
                        "requested_time": appointment_time,
                        "existing_appointment_id": existing_appointment['id']
                    }
                )
            )
            log_exception(conflict_exception)
            return translate_response(conflict_exception.user_message, language)

        appointment = Appointment(
            client_name=client_name,
            phone_number=phone_number,
            service_type=service_type,
            appointment_date=date_obj,
            appointment_time=time_obj.strftime("%H:%M"),
            location=location,
            duration=duration,
            is_recurring=is_recurring,
            recurrence_pattern=recurrence_pattern,
            serial_number="TEMP"
        )

        logger.info("\nSaving to database...")
        try:
            saved_appointment = db.create_appointment(appointment)
            logger.info(f"Appointment saved successfully with ID: {saved_appointment.id}")
            logger.info(f"Serial Number: {saved_appointment.serial_number}")

            try:
                verified_appointment = db.get_appointment_by_id(saved_appointment.id)
                if verified_appointment:
                    logger.info(f"Verified appointment in database: {verified_appointment}")
                else:
                    logger.error(f"Appointment {saved_appointment.id} not found in database after saving")
            except Exception as verify_error:
                logger.error(f"Error verifying appointment: {str(verify_error)}")

            service_display = SERVICE_NAME_MAP.get(service_type, {"en": service_type, "ar": service_type})[language]
            confirmation_message = (
                f"Appointment confirmed!\n\n"
                f"Name: {client_name}\n"
                f"Service: {service_display}\n"
                f"Date: {appointment_date}\n"
                f"Time: {appointment_time}\n"
                f"Location: {location or 'Not specified'}\n"
                f"Serial Number: {saved_appointment.serial_number}\n\n"
                f"Thank you for booking with us!"
            )

            formatted_phone = ''.join(filter(str.isdigit, phone_number))
            if not formatted_phone.startswith('whatsapp:'):
                formatted_phone = f'whatsapp:{formatted_phone}'

            try:
                twilio.send_message(formatted_phone, translate_response(confirmation_message, language))
                logger.info("Confirmation message sent successfully")
            except Exception as e:
                # Use ExternalAPIException for Twilio errors
                twilio_exception = ExternalAPIException(
                    message=f"Failed to send WhatsApp confirmation message",
                    service="Twilio",
                    original_exception=e,
                    context=ErrorContext(
                        user_id=phone_number,
                        operation="send_confirmation_message",
                        additional_data={"appointment_id": saved_appointment.id}
                    )
                )
                log_exception(twilio_exception)
                # Don't fail the booking if message sending fails

            response = f"Appointment booked successfully! ID: {saved_appointment.id}"
            return translate_response(response, language)

        except Exception as db_error:
            # Use standardized error response handling
            return create_standardized_error_response(
                error=db_error,
                language=language,
                user_id=phone_number,
                operation="save_appointment",
                additional_context={"client_name": client_name, "service_type": service_type}
            )

    except Exception as e:
        # Use standardized error response handling for unexpected errors
        return create_standardized_error_response(
            error=e,
            language=language,
            user_id=phone_number,
            operation="book_appointment",
            additional_context={"error_type": type(e).__name__}
        )

@tool
def check_availability(date: str, phone_number: str) -> str:
    """Check available time slots for a given date."""
    try:
        language = get_booking_context(phone_number).get('language', 'en')

        # Validate and sanitize inputs
        date = sanitize_text_input(date.strip()) if date else ""
        phone_number = sanitize_text_input(phone_number.strip()) if phone_number else ""

        if not date:
            response = "Please provide a valid date."
            return translate_response(response, language)

        try:
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            # Check if date is not in the past
            if date_obj.date() < datetime.now().date():
                response = "Please provide a future date."
                return translate_response(response, language)
        except ValueError:
            response = "Invalid date format. Please use YYYY-MM-DD format."
            return translate_response(response, language)

        available_slots = db.get_available_time_slots(date_obj)

        if not available_slots:
            logger.warning(f"No available slots for {date}")
            response = f"No available slots for {date}. Please try another date."
            return translate_response(response, language)

        logger.info(f"Available slots for {date}: {available_slots}")
        response = f"Available time slots for {date}:\n" + "\n".join(available_slots)
        return translate_response(response, language)
    except Exception as e:
        # Use standardized error response handling
        return create_standardized_error_response(
            error=e,
            language=language,
            user_id=phone_number,
            operation="check_availability",
            additional_context={"requested_date": date}
        )

@tool
def list_services(phone_number: str) -> str:
    """List all available services."""
    language = get_booking_context(phone_number).get('language', 'en')
    services = [SERVICE_NAME_MAP[service][language] for service in AVAILABLE_SERVICES]
    response = "Available services:\n" + "\n".join(f"- {service}" for service in services)
    return translate_response(response, language)

@tool
def verify_appointment(phone_number: str) -> str:
    """Verify if an appointment was stored in the database."""
    language = get_booking_context(phone_number).get('language', 'en')
    try:
        # Validate and sanitize phone number
        phone_number = sanitize_text_input(phone_number.strip()) if phone_number else ""

        if not phone_number:
            response = "Invalid phone number provided."
            return translate_response(response, language)

        appointments = db.get_appointments_by_phone(phone_number)
        if appointments:
            latest_appointment = appointments[-1]
            service_display = SERVICE_NAME_MAP.get(latest_appointment.service_type, {"en": latest_appointment.service_type, "ar": latest_appointment.service_type})[language]
            response = (
                f"Your appointment was successfully stored in the database!\n\n"
                f"Details:\n"
                f"Name: {latest_appointment.client_name}\n"
                f"Service: {service_display}\n"
                f"Date: {latest_appointment.appointment_date.strftime('%Y-%m-%d')}\n"
                f"Time: {latest_appointment.appointment_time}\n"
                f"Status: {latest_appointment.status}"
            )
            return translate_response(response, language)
        response = "No appointment found in the database for this phone number."
        return translate_response(response, language)
    except Exception as e:
        logger.error(f"Error verifying appointment: {str(e)}")
        safe_message = "Error verifying appointment. Please try again."
        return translate_response(safe_message, language)

@tool
def list_all_appointments(phone_number: str) -> str:
    """List all appointments stored in the database."""
    language = get_booking_context(phone_number).get('language', 'en')
    try:
        # Use consolidated database operation from DatabaseManager
        appointments = db.get_all_appointments_ordered()

        if not appointments:
            response = "No appointments found in the database."
            return translate_response(response, language)

        response = "All appointments in the database:\n\n"
        for appt in appointments:
            service_display = SERVICE_NAME_MAP.get(appt['service_type'], {"en": appt['service_type'], "ar": appt['service_type']})[language]
            response += (
                f"ID: {appt['id']}\n"
                f"Name: {appt['client_name']}\n"
                f"Phone: {appt['phone_number']}\n"
                f"Service: {service_display}\n"
                f"Date: {appt['appointment_date']}\n"
                f"Time: {appt['appointment_time']}\n"
                f"Status: {appt['status']}\n"
                f"Created: {appt['created_at']}\n"
                f"{'-'*50}\n"
            )
        return translate_response(response, language)
    except Exception as e:
        # Use standardized error response handling
        return create_standardized_error_response(
            error=e,
            language=language,
            user_id=phone_number,
            operation="list_all_appointments"
        )

@tool
def sync_appointment_to_odoo(appointment_id: int, phone_number: str) -> str:
    """Sync a specific appointment to Odoo CRM. Returns success or error message."""
    language = get_booking_context(phone_number).get('language', 'en')
    try:
        # Validate inputs
        if not isinstance(appointment_id, int) or appointment_id <= 0:
            response = "Invalid appointment ID provided."
            return translate_response(response, language)

        phone_number = sanitize_text_input(phone_number.strip()) if phone_number else ""
        if not phone_number:
            response = "Invalid phone number provided."
            return translate_response(response, language)

        appointment = db.get_appointment_by_id(appointment_id)
        if not appointment:
            response = f"Appointment with ID {appointment_id} not found."
            return translate_response(response, language)

        # Try to get client data, but handle permission errors gracefully
        try:
            client = db.get_client(appointment.phone_number)
        except Exception as e:
            logger.warning(f"Could not retrieve client data (permission issue): {str(e)}")
            client = None

        if not client:
            # Use default client data if not available
            client = {
                'contract_type': 'one-time',
                'most_asked_provider': None
            }

        appointment_data = {
            "id": appointment.id,
            "client_name": appointment.client_name,
            "phone_number": appointment.phone_number,
            "service_type": appointment.service_type,
            "appointment_date": appointment.appointment_date,
            "appointment_time": appointment.appointment_time,
            "location": appointment.location,
            "contract_type": client.get('contract_type', 'one-time'),
            "most_asked_provider": client.get('most_asked_provider')
        }

        # Get Odoo integration instance
        odoo = get_odoo_integration()
        if not odoo:
            response = f"Odoo integration not available for appointment {appointment_id}"
            return translate_response(response, language)

        logger.info(f"Syncing appointment {appointment_id} to Odoo via REST API")

        # Sync appointment using REST API
        result = odoo.sync_appointment(appointment_data)

        if result['success']:
            response = f"Successfully synced appointment {appointment_id} to Odoo (Booking ID: {result['booking_id']})"
            logger.info(f"Successfully synced appointment {appointment_id} - Customer ID: {result['customer_id']}, Booking ID: {result['booking_id']}")
        else:
            response = f"Failed to sync appointment {appointment_id} to Odoo: {result['error']}"
            logger.error(f"Failed to sync appointment {appointment_id}: {result['error']}")

        return translate_response(response, language)

    except Exception as e:
        logger.error(f"Error syncing appointment to Odoo: {str(e)}")
        response = f"Error syncing appointment to Odoo: {str(e)}"
        return translate_response(response, language)

@tool
def sync_all_pending_appointments(phone_number: str) -> str:
    """Sync all pending appointments to Odoo CRM. Returns summary of processed appointments."""
    language = get_booking_context(phone_number).get('language', 'en')
    try:
        # Get Odoo integration instance
        odoo = get_odoo_integration()
        if not odoo:
            response = "Odoo integration not available"
            return translate_response(response, language)

        result = odoo.process_appointments()

        if result.get('processed', 0) > 0:
            response = f"Successfully synced {result['processed']} appointments to Odoo."
            return translate_response(response, language)
        else:
            response = f"No appointments were synced. Error: {result.get('errors', 'No pending appointments')}"
            return translate_response(response, language)

    except Exception as e:
        response = f"Error syncing appointments to Odoo: {str(e)}"
        return translate_response(response, language)

@tool
def verify_odoo_sync(appointment_id: int, phone_number: str) -> str:
    """Verify if an appointment was successfully synced to Odoo."""
    language = get_booking_context(phone_number).get('language', 'en')
    try:
        appointment = db.get_appointment_by_id(appointment_id)
        if not appointment:
            response = f"Appointment with ID {appointment_id} not found."
            return translate_response(response, language)

        if appointment.odoo_lead_id:
            response = f"Appointment {appointment_id} is synced to Odoo with Lead ID: {appointment.odoo_lead_id}"
            return translate_response(response, language)
        else:
            response = f"Appointment {appointment_id} is not yet synced to Odoo."
            return translate_response(response, language)

    except Exception as e:
        response = f"Error verifying Odoo sync: {str(e)}"
        return translate_response(response, language)

tools = [
    book_appointment,
    check_availability,
    list_services,
    verify_appointment,
    list_all_appointments,
    sync_appointment_to_odoo,
    sync_all_pending_appointments,
    verify_odoo_sync
]