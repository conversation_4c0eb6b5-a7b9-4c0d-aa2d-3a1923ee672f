{"info": {"_postman_id": "f8a7b3e5-6d2c-4a5e-9f8d-1c2e3b4a5c6d", "name": "Smart Booking System API", "description": "# Smart Booking System API\n\nThis collection contains requests for testing the Smart Booking System REST API.\n\n## Setup\n\n1. Set the `base_url` variable to your Odoo.sh instance URL (e.g., `https://your-instance.odoo.sh`)\n2. Set the `api_key` variable to your Odoo API key\n\n## Authentication\n\nAll requests use the API key for authentication. The key is included in the request headers as `api-key`.\n\n## Environment Variables\n\n- `base_url`: The base URL of your Odoo instance\n- `api_key`: Your Odoo API key\n", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "https://your-instance.odoo.sh", "type": "string"}, {"key": "api_key", "value": "your-api-key", "type": "string"}], "item": [{"name": "Bookings", "item": [{"name": "Get All Bookings", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/booking", "host": ["{{base_url}}"], "path": ["api", "booking"]}, "description": "Get all bookings"}, "response": []}, {"name": "Get Booking by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/booking/1", "host": ["{{base_url}}"], "path": ["api", "booking", "1"]}, "description": "Get a specific booking by ID"}, "response": []}, {"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"worker_id\": 2,\n  \"service_id\": 3,\n  \"booking_date\": \"2023-06-15\",\n  \"start_time\": \"2023-06-15 10:00:00\",\n  \"end_time\": \"2023-06-15 12:00:00\"\n}"}, "url": {"raw": "{{base_url}}/api/booking", "host": ["{{base_url}}"], "path": ["api", "booking"]}, "description": "Create a new booking"}, "response": []}, {"name": "Update Booking", "request": {"method": "PUT", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"booking_date\": \"2023-06-16\",\n  \"start_time\": \"2023-06-16 11:00:00\",\n  \"end_time\": \"2023-06-16 13:00:00\"\n}"}, "url": {"raw": "{{base_url}}/api/booking/1", "host": ["{{base_url}}"], "path": ["api", "booking", "1"]}, "description": "Update an existing booking"}, "response": []}, {"name": "Delete Booking", "request": {"method": "DELETE", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/booking/1", "host": ["{{base_url}}"], "path": ["api", "booking", "1"]}, "description": "Delete a booking"}, "response": []}, {"name": "Confirm Booking", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/booking/1/confirm", "host": ["{{base_url}}"], "path": ["api", "booking", "1", "confirm"]}, "description": "Confirm a booking"}, "response": []}, {"name": "Cancel Booking", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/booking/1/cancel", "host": ["{{base_url}}"], "path": ["api", "booking", "1", "cancel"]}, "description": "Cancel a booking"}, "response": []}]}, {"name": "Customers", "item": [{"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/customer", "host": ["{{base_url}}"], "path": ["api", "customer"]}, "description": "Get all customers"}, "response": []}, {"name": "Get Customer by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/customer/1", "host": ["{{base_url}}"], "path": ["api", "customer", "1"]}, "description": "Get a specific customer by ID"}, "response": []}, {"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"gps_coordinates\": \"40.7128,-74.0060\"\n}"}, "url": {"raw": "{{base_url}}/api/customer", "host": ["{{base_url}}"], "path": ["api", "customer"]}, "description": "Create a new customer"}, "response": []}, {"name": "Update Customer", "request": {"method": "PUT", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/customer/1", "host": ["{{base_url}}"], "path": ["api", "customer", "1"]}, "description": "Update an existing customer"}, "response": []}, {"name": "Add Preferred Worker", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/customer/1/preferred_workers", "host": ["{{base_url}}"], "path": ["api", "customer", "1", "preferred_workers"]}, "description": "Add a worker to customer's preferred workers list"}, "response": []}]}, {"name": "Workers", "item": [{"name": "Get All Workers", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/worker", "host": ["{{base_url}}"], "path": ["api", "worker"]}, "description": "Get all workers"}, "response": []}, {"name": "Get Worker by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/worker/1", "host": ["{{base_url}}"], "path": ["api", "worker", "1"]}, "description": "Get a specific worker by ID"}, "response": []}]}, {"name": "Contracts", "item": [{"name": "Get All Contracts", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/contract", "host": ["{{base_url}}"], "path": ["api", "contract"]}, "description": "Get all contracts"}, "response": []}, {"name": "Get Contract by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/contract/1", "host": ["{{base_url}}"], "path": ["api", "contract", "1"]}, "description": "Get a specific contract by ID"}, "response": []}, {"name": "Create Contract", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"worker_id\": 2,\n  \"start_time\": \"2023-06-15 10:00:00\",\n  \"end_time\": \"2023-07-15 10:00:00\",\n  \"contract_type\": \"multiple\"\n}"}, "url": {"raw": "{{base_url}}/api/contract", "host": ["{{base_url}}"], "path": ["api", "contract"]}, "description": "Create a new contract"}, "response": []}, {"name": "Confirm Contract", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/contract/1/confirm", "host": ["{{base_url}}"], "path": ["api", "contract", "1", "confirm"]}, "description": "Confirm a contract"}, "response": []}, {"name": "Cancel Contract", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/contract/1/cancel", "host": ["{{base_url}}"], "path": ["api", "contract", "1", "cancel"]}, "description": "Cancel a contract"}, "response": []}, {"name": "Create Invoice", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/contract/1/create_invoice", "host": ["{{base_url}}"], "path": ["api", "contract", "1", "create_invoice"]}, "description": "Create an invoice for a contract"}, "response": []}]}, {"name": "Wallets", "item": [{"name": "Get All Wallets", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/wallet", "host": ["{{base_url}}"], "path": ["api", "wallet"]}, "description": "Get all wallets"}, "response": []}, {"name": "Get Wallet by ID", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/wallet/1", "host": ["{{base_url}}"], "path": ["api", "wallet", "1"]}, "description": "Get a specific wallet by ID"}, "response": []}, {"name": "Create Wallet", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>'s Wallet\",\n  \"partner_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/wallet", "host": ["{{base_url}}"], "path": ["api", "wallet"]}, "description": "Create a new wallet"}, "response": []}, {"name": "Create Wallet Transaction", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.0,\n  \"type\": \"payment\"\n}"}, "url": {"raw": "{{base_url}}/api/wallet/1/transaction", "host": ["{{base_url}}"], "path": ["api", "wallet", "1", "transaction"]}, "description": "Create a new wallet transaction"}, "response": []}]}, {"name": "Ratings", "item": [{"name": "Get All Ratings", "request": {"method": "GET", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rating", "host": ["{{base_url}}"], "path": ["api", "rating"]}, "description": "Get all ratings"}, "response": []}, {"name": "Create Rating", "request": {"method": "POST", "header": [{"key": "api-key", "value": "{{api_key}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"worker_id\": 2,\n  \"booking_id\": 3,\n  \"rating\": \"4\",\n  \"comments\": \"Great service!\"\n}"}, "url": {"raw": "{{base_url}}/api/rating", "host": ["{{base_url}}"], "path": ["api", "rating"]}, "description": "Create a new rating"}, "response": []}]}]}